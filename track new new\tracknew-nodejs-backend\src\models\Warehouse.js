const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Warehouse = sequelize.define('Warehouse', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  warehouse_code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  warehouse_name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 255]
    }
  },
  warehouse_type: {
    type: DataTypes.ENUM('main', 'branch', 'virtual', 'consignment'),
    defaultValue: 'main'
  },
  address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  city: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  state: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  country: {
    type: DataTypes.STRING(100),
    allowNull: true,
    defaultValue: 'India'
  },
  pincode: {
    type: DataTypes.STRING(10),
    allowNull: true
  },
  contact_person: {
    type: DataTypes.STRING,
    allowNull: true
  },
  phone_number: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  email: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isEmail: true
    }
  },
  manager_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  capacity: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Storage capacity in square feet'
  },
  current_utilization: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0,
    comment: 'Current utilization percentage'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  is_default: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  allow_negative_stock: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  auto_reorder_enabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  operating_hours: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  facilities: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'warehouses',
  paranoid: true,
  indexes: [
    {
      fields: ['warehouse_code']
    },
    {
      fields: ['company_id']
    },
    {
      fields: ['warehouse_name']
    },
    {
      fields: ['warehouse_type']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['is_default']
    },
    {
      fields: ['manager_id']
    }
  ],
  hooks: {
    beforeCreate: async (warehouse) => {
      if (!warehouse.warehouse_code) {
        const count = await Warehouse.count({ where: { company_id: warehouse.company_id } });
        warehouse.warehouse_code = `WH-${warehouse.company_id}-${String(count + 1).padStart(4, '0')}`;
      }
    }
  }
});

module.exports = Warehouse;
