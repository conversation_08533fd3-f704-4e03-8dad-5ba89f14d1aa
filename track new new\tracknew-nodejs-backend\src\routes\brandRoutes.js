const express = require('express');
const { body } = require('express-validator');

const brandController = require('../controllers/brandController');
const { protect, restrictTo, restrictToCompany } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Protect all routes
router.use(protect);
router.use(restrictToCompany);

// Validation rules
const createBrandValidation = [
  body('brand_name')
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('Brand name is required and must be between 2-255 characters'),
  body('brand_code')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Brand code must be less than 50 characters'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('phone')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Phone number must be less than 20 characters'),
  body('website')
    .optional()
    .isURL()
    .withMessage('Please provide a valid website URL'),
  body('status')
    .optional()
    .isBoolean()
    .withMessage('Status must be a boolean value')
];

const updateBrandValidation = [
  body('brand_name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('Brand name must be between 2-255 characters'),
  body('brand_code')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Brand code must be less than 50 characters'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('phone')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Phone number must be less than 20 characters'),
  body('website')
    .optional()
    .isURL()
    .withMessage('Please provide a valid website URL'),
  body('status')
    .optional()
    .isBoolean()
    .withMessage('Status must be a boolean value')
];

// Routes
router.get('/stats', brandController.getBrandStats);

router
  .route('/')
  .get(brandController.getBrands)
  .post(createBrandValidation, validateRequest, brandController.createBrand);

router
  .route('/:id')
  .get(brandController.getBrand)
  .put(updateBrandValidation, validateRequest, brandController.updateBrand)
  .delete(restrictTo('admin', 'sub_admin'), brandController.deleteBrand);

module.exports = router;
