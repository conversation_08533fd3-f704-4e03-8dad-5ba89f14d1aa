const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const RMAItem = sequelize.define('RMAItem', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  rma_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'rmas',
      key: 'id'
    }
  },
  product_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  item_name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  item_description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  item_code: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  serial_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  batch_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  quantity: {
    type: DataTypes.DECIMAL(10, 3),
    allowNull: false,
    defaultValue: 1
  },
  unit_price: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  total_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  condition_received: {
    type: DataTypes.ENUM('new', 'good', 'fair', 'poor', 'damaged', 'defective'),
    allowNull: true
  },
  condition_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  defect_description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  repair_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  repair_cost: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0
  },
  replacement_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  replacement_product_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  refund_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0
  },
  warranty_status: {
    type: DataTypes.ENUM('valid', 'expired', 'void', 'unknown'),
    allowNull: true
  },
  warranty_expiry_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  purchase_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  invoice_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  disposition: {
    type: DataTypes.ENUM('return_to_customer', 'repair', 'replace', 'refund', 'dispose', 'return_to_vendor'),
    allowNull: true
  },
  disposition_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  documents: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  inspection_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  inspected_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  inspection_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('pending', 'received', 'inspected', 'approved', 'rejected', 'processed'),
    defaultValue: 'pending'
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  }
}, {
  tableName: 'rma_items',
  indexes: [
    {
      fields: ['rma_id']
    },
    {
      fields: ['product_id']
    },
    {
      fields: ['serial_number']
    },
    {
      fields: ['batch_number']
    },
    {
      fields: ['status']
    },
    {
      fields: ['warranty_status']
    },
    {
      fields: ['disposition']
    }
  ],
  hooks: {
    beforeSave: (item) => {
      item.total_amount = item.quantity * item.unit_price;
    }
  }
});

module.exports = RMAItem;
