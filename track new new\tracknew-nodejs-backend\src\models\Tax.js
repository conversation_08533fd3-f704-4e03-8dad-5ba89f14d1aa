const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Tax = sequelize.define('Tax', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  tax_name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 100]
    }
  },
  tax_code: {
    type: DataTypes.STRING(20),
    allowNull: false
  },
  tax_type: {
    type: DataTypes.ENUM('gst', 'vat', 'service_tax', 'excise', 'customs', 'cess', 'other'),
    allowNull: false
  },
  tax_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    validate: {
      min: 0,
      max: 100
    }
  },
  cgst_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0
  },
  sgst_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0
  },
  igst_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0
  },
  cess_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0
  },
  is_compound: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this tax is calculated on top of other taxes'
  },
  is_inclusive: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this tax is included in the price'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  effective_from: {
    type: DataTypes.DATE,
    allowNull: true
  },
  effective_to: {
    type: DataTypes.DATE,
    allowNull: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'taxes',
  paranoid: true,
  indexes: [
    {
      unique: true,
      fields: ['company_id', 'tax_code']
    },
    {
      fields: ['company_id']
    },
    {
      fields: ['tax_type']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['effective_from']
    },
    {
      fields: ['effective_to']
    }
  ]
});

module.exports = Tax;
