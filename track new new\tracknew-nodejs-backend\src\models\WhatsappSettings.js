const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const WhatsappSettings = sequelize.define('WhatsappSettings', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  provider: {
    type: DataTypes.ENUM('whatsapp_business_api', 'twilio', 'gupshup', 'interakt', 'wati', 'custom'),
    allowNull: false
  },
  provider_name: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  phone_number: {
    type: DataTypes.STRING(20),
    allowNull: false
  },
  phone_number_id: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  business_account_id: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  access_token: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  app_id: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  app_secret: {
    type: DataTypes.STRING,
    allowNull: true
  },
  webhook_url: {
    type: DataTypes.STRING,
    allowNull: true
  },
  webhook_token: {
    type: DataTypes.STRING,
    allowNull: true
  },
  verify_token: {
    type: DataTypes.STRING,
    allowNull: true
  },
  api_url: {
    type: DataTypes.STRING,
    allowNull: true
  },
  api_key: {
    type: DataTypes.STRING,
    allowNull: true
  },
  api_secret: {
    type: DataTypes.STRING,
    allowNull: true
  },
  username: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  password: {
    type: DataTypes.STRING,
    allowNull: true
  },
  instance_id: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  qr_code: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  connection_status: {
    type: DataTypes.ENUM('disconnected', 'connecting', 'connected', 'error'),
    defaultValue: 'disconnected'
  },
  last_connected_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  session_data: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  is_verified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  test_mode: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  rate_limit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Messages per minute limit'
  },
  daily_limit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Daily message limit'
  },
  monthly_limit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Monthly message limit'
  },
  messages_sent_today: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  messages_sent_month: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  last_reset_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  templates: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  auto_notifications: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {
      service_created: false,
      service_completed: false,
      payment_received: false,
      invoice_sent: false,
      reminder_due: false
    }
  },
  notification_settings: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  business_profile: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  configuration: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  error_log: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  last_error: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  last_success_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  last_error_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'whatsapp_settings',
  indexes: [
    {
      fields: ['company_id']
    },
    {
      fields: ['provider']
    },
    {
      fields: ['phone_number']
    },
    {
      fields: ['connection_status']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['is_verified']
    }
  ]
});

module.exports = WhatsappSettings;
