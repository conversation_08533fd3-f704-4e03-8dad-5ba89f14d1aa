const express = require('express');
const { body } = require('express-validator');

const salesController = require('../controllers/salesController');
const { protect, restrictTo, restrictToCompany } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Protect all routes
router.use(protect);
router.use(restrictToCompany);

// Validation rules
const createSaleValidation = [
  body('customer_id')
    .isInt({ min: 1 })
    .withMessage('Customer ID is required and must be a valid number'),
  body('sales_date')
    .optional()
    .isISO8601()
    .withMessage('Sales date must be a valid date'),
  body('delivery_date')
    .optional()
    .isISO8601()
    .withMessage('Delivery date must be a valid date'),
  body('items')
    .isArray({ min: 1 })
    .withMessage('At least one item is required'),
  body('items.*.item_name')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Item name is required and must be less than 255 characters'),
  body('items.*.quantity')
    .isFloat({ min: 0.001 })
    .withMessage('Quantity must be greater than 0'),
  body('items.*.unit_price')
    .isFloat({ min: 0 })
    .withMessage('Unit price must be a positive number'),
  body('discount_type')
    .optional()
    .isIn(['percentage', 'fixed'])
    .withMessage('Discount type must be percentage or fixed'),
  body('discount_value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Discount value must be a positive number'),
  body('shipping_amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Shipping amount must be a positive number'),
  body('adjustment_amount')
    .optional()
    .isFloat()
    .withMessage('Adjustment amount must be a valid number')
];

const updateSaleValidation = [
  body('delivery_date')
    .optional()
    .isISO8601()
    .withMessage('Delivery date must be a valid date'),
  body('status')
    .optional()
    .isIn(['draft', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'])
    .withMessage('Invalid status value'),
  body('payment_status')
    .optional()
    .isIn(['unpaid', 'partially_paid', 'paid', 'overpaid'])
    .withMessage('Invalid payment status value'),
  body('adjustment_amount')
    .optional()
    .isFloat()
    .withMessage('Adjustment amount must be a valid number')
];

// Routes
router.get('/stats', salesController.getSalesStats);

router
  .route('/')
  .get(salesController.getSales)
  .post(createSaleValidation, validateRequest, salesController.createSale);

router
  .route('/:id')
  .get(salesController.getSale)
  .put(updateSaleValidation, validateRequest, salesController.updateSale)
  .delete(restrictTo('admin', 'sub_admin'), salesController.deleteSale);

module.exports = router;
