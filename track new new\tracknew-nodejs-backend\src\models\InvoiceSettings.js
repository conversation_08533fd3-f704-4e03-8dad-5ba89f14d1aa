const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const InvoiceSettings = sequelize.define('InvoiceSettings', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  invoice_prefix: {
    type: DataTypes.STRING(20),
    allowNull: true,
    defaultValue: 'INV'
  },
  invoice_suffix: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  invoice_number_format: {
    type: DataTypes.STRING(50),
    allowNull: true,
    defaultValue: '{prefix}-{number}'
  },
  starting_number: {
    type: DataTypes.INTEGER,
    defaultValue: 1
  },
  current_number: {
    type: DataTypes.INTEGER,
    defaultValue: 1
  },
  number_padding: {
    type: DataTypes.INTEGER,
    defaultValue: 6
  },
  reset_yearly: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  reset_monthly: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  due_days: {
    type: DataTypes.INTEGER,
    defaultValue: 30
  },
  late_fee_type: {
    type: DataTypes.ENUM('percentage', 'fixed'),
    allowNull: true
  },
  late_fee_value: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0
  },
  auto_send_invoice: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  auto_send_reminder: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  reminder_days_before: {
    type: DataTypes.INTEGER,
    defaultValue: 3
  },
  reminder_days_after: {
    type: DataTypes.INTEGER,
    defaultValue: 7
  },
  default_template_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'invoice_templates',
      key: 'id'
    }
  },
  default_currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'INR'
  },
  tax_inclusive: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  show_tax_number: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  show_discount: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  show_shipping: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  terms_conditions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  footer_text: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  signature_image: {
    type: DataTypes.STRING,
    allowNull: true
  },
  logo_image: {
    type: DataTypes.STRING,
    allowNull: true
  },
  watermark_image: {
    type: DataTypes.STRING,
    allowNull: true
  },
  email_subject: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: 'Invoice #{invoice_number} from {company_name}'
  },
  email_body: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  sms_template: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  whatsapp_template: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'invoice_settings',
  indexes: [
    {
      unique: true,
      fields: ['company_id']
    },
    {
      fields: ['default_template_id']
    }
  ]
});

module.exports = InvoiceSettings;
