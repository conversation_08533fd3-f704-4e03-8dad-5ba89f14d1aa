const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const AMCDates = sequelize.define('AMCDates', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  amc_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'amc',
      key: 'id'
    }
  },
  service_date: {
    type: DataTypes.DATE,
    allowNull: false
  },
  service_status: {
    type: DataTypes.ENUM('pending', 'completed', 'cancelled', 'rescheduled'),
    defaultValue: 'pending'
  },
  service_description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  technician_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  customer_feedback: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  rating: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 5
    }
  },
  service_cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0
  },
  parts_used: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  service_duration: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Service duration in minutes'
  },
  next_service_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  reminder_sent: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  reminder_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  assigned_technician: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  completion_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  is_billable: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  invoice_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'invoices',
      key: 'id'
    }
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'amc_dates',
  paranoid: true,
  indexes: [
    {
      fields: ['amc_id']
    },
    {
      fields: ['service_date']
    },
    {
      fields: ['service_status']
    },
    {
      fields: ['assigned_technician']
    }
  ]
});

module.exports = AMCDates;
