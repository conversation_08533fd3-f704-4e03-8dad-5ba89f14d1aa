const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const CompanySites = sequelize.define('CompanySites', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  website_name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  username: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true
  },
  domain_name: {
    type: DataTypes.STRING,
    allowNull: true
  },
  domain_status: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '0=inactive, 1=active, 2=pending'
  },
  domain_method: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  template_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'website_templates',
      key: 'id'
    }
  },
  theme_path: {
    type: DataTypes.STRING,
    allowNull: true
  },
  theme_settings: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  pages: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  company_data: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  image_data: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  header: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  footer: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  primary_data: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  services: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  products: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  testimonials: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  company_video: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  history: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  gallery: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  contact_forms: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  seo_settings: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  analytics_code: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  custom_css: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  custom_js: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  is_published: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  ssl_enabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  last_published_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'company_sites',
  paranoid: true,
  indexes: [
    {
      fields: ['company_id']
    },
    {
      unique: true,
      fields: ['username']
    },
    {
      fields: ['domain_name']
    },
    {
      fields: ['template_id']
    },
    {
      fields: ['is_published']
    },
    {
      fields: ['is_active']
    }
  ]
});

module.exports = CompanySites;
