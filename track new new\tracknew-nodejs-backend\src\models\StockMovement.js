const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const StockMovement = sequelize.define('StockMovement', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  product_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  warehouse_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'warehouses',
      key: 'id'
    }
  },
  movement_type: {
    type: DataTypes.ENUM('in', 'out', 'transfer', 'adjustment'),
    allowNull: false
  },
  transaction_type: {
    type: DataTypes.ENUM('purchase', 'sales', 'return', 'adjustment', 'transfer', 'opening_stock', 'production', 'consumption'),
    allowNull: false
  },
  reference_type: {
    type: DataTypes.ENUM('purchase_order', 'sales_order', 'invoice', 'service', 'adjustment', 'transfer', 'opening_stock'),
    allowNull: true
  },
  reference_id: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  reference_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  movement_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  quantity: {
    type: DataTypes.DECIMAL(10, 3),
    allowNull: false
  },
  unit_cost: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  total_cost: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  running_balance: {
    type: DataTypes.DECIMAL(10, 3),
    allowNull: false,
    defaultValue: 0
  },
  batch_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  serial_numbers: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  expiry_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  from_warehouse_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'warehouses',
      key: 'id'
    }
  },
  to_warehouse_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'warehouses',
      key: 'id'
    }
  },
  reason: {
    type: DataTypes.STRING,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'stock_movements',
  indexes: [
    {
      fields: ['company_id']
    },
    {
      fields: ['product_id']
    },
    {
      fields: ['warehouse_id']
    },
    {
      fields: ['movement_type']
    },
    {
      fields: ['transaction_type']
    },
    {
      fields: ['reference_type', 'reference_id']
    },
    {
      fields: ['movement_date']
    },
    {
      fields: ['batch_number']
    }
  ]
});

module.exports = StockMovement;
