const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const MessageTransactions = sequelize.define('MessageTransactions', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  message_type: {
    type: DataTypes.ENUM('sms', 'whatsapp', 'email'),
    allowNull: false
  },
  message_id: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  provider: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  provider_message_id: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  to_number: {
    type: DataTypes.STRING(20),
    allowNull: false
  },
  from_number: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  message_content: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  message_template: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  template_variables: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  media_urls: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  status: {
    type: DataTypes.ENUM('pending', 'sent', 'delivered', 'read', 'failed', 'rejected'),
    defaultValue: 'pending'
  },
  delivery_status: {
    type: DataTypes.ENUM('unknown', 'sent', 'delivered', 'read', 'failed', 'undelivered'),
    defaultValue: 'unknown'
  },
  error_code: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  cost: {
    type: DataTypes.DECIMAL(8, 4),
    allowNull: true,
    defaultValue: 0
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'INR'
  },
  sent_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  delivered_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  read_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  failed_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  retry_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  max_retries: {
    type: DataTypes.INTEGER,
    defaultValue: 3
  },
  next_retry_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  priority: {
    type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
    defaultValue: 'normal'
  },
  scheduled_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  reference_type: {
    type: DataTypes.ENUM('service', 'invoice', 'payment', 'reminder', 'marketing', 'otp', 'notification'),
    allowNull: true
  },
  reference_id: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  customer_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'customers',
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  campaign_id: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  webhook_data: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  provider_response: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  is_bulk: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  bulk_id: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'message_transactions',
  indexes: [
    {
      fields: ['company_id']
    },
    {
      fields: ['message_type']
    },
    {
      fields: ['provider']
    },
    {
      fields: ['to_number']
    },
    {
      fields: ['status']
    },
    {
      fields: ['delivery_status']
    },
    {
      fields: ['reference_type', 'reference_id']
    },
    {
      fields: ['customer_id']
    },
    {
      fields: ['sent_at']
    },
    {
      fields: ['scheduled_at']
    },
    {
      fields: ['bulk_id']
    },
    {
      fields: ['provider_message_id']
    }
  ]
});

module.exports = MessageTransactions;
