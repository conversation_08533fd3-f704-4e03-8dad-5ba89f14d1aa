const express = require('express');
const { body } = require('express-validator');

const proformaController = require('../controllers/proformaController');
const { protect, restrictTo, restrictToCompany } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Protect all routes
router.use(protect);
router.use(restrictToCompany);

// Validation rules
const createProformaValidation = [
  body('customer_id')
    .isInt({ min: 1 })
    .withMessage('Customer ID is required and must be a valid number'),
  body('proforma_date')
    .optional()
    .isISO8601()
    .withMessage('Proforma date must be a valid date'),
  body('valid_until')
    .optional()
    .isISO8601()
    .withMessage('Valid until date must be a valid date'),
  body('items')
    .isArray({ min: 1 })
    .withMessage('At least one item is required'),
  body('items.*.item_name')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Item name is required and must be less than 255 characters'),
  body('items.*.quantity')
    .isFloat({ min: 0.001 })
    .withMessage('Quantity must be greater than 0'),
  body('items.*.unit_price')
    .isFloat({ min: 0 })
    .withMessage('Unit price must be a positive number'),
  body('items.*.discount_type')
    .optional()
    .isIn(['percentage', 'fixed'])
    .withMessage('Discount type must be percentage or fixed'),
  body('items.*.discount_value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Discount value must be a positive number'),
  body('items.*.cgst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('CGST rate must be between 0 and 100'),
  body('items.*.sgst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('SGST rate must be between 0 and 100'),
  body('items.*.igst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('IGST rate must be between 0 and 100'),
  body('discount_type')
    .optional()
    .isIn(['percentage', 'fixed'])
    .withMessage('Discount type must be percentage or fixed'),
  body('discount_value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Discount value must be a positive number'),
  body('shipping_amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Shipping amount must be a positive number'),
  body('payment_terms')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Payment terms must be less than 100 characters'),
  body('delivery_terms')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Delivery terms must be less than 100 characters')
];

const updateProformaValidation = [
  body('valid_until')
    .optional()
    .isISO8601()
    .withMessage('Valid until date must be a valid date'),
  body('status')
    .optional()
    .isIn(['draft', 'sent', 'accepted', 'rejected', 'expired', 'converted'])
    .withMessage('Invalid status value'),
  body('payment_terms')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Payment terms must be less than 100 characters'),
  body('delivery_terms')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Delivery terms must be less than 100 characters'),
  body('terms_conditions')
    .optional()
    .trim()
    .isLength({ max: 5000 })
    .withMessage('Terms and conditions must be less than 5000 characters'),
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Notes must be less than 2000 characters'),
  body('internal_notes')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Internal notes must be less than 2000 characters')
];

// Routes
router
  .route('/')
  .get(proformaController.getProformas)
  .post(createProformaValidation, validateRequest, proformaController.createProforma);

router
  .route('/:id')
  .get(proformaController.getProforma)
  .put(updateProformaValidation, validateRequest, proformaController.updateProforma)
  .delete(restrictTo('admin', 'sub_admin'), proformaController.deleteProforma);

module.exports = router;
