const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const SmsSetting = sequelize.define('SmsSetting', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  provider: {
    type: DataTypes.ENUM('twilio', 'textlocal', 'msg91', 'fast2sms', 'nexmo', 'aws_sns', 'custom'),
    allowNull: false
  },
  provider_name: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  api_key: {
    type: DataTypes.STRING,
    allowNull: true
  },
  api_secret: {
    type: DataTypes.STRING,
    allowNull: true
  },
  sender_id: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  api_url: {
    type: DataTypes.STRING,
    allowNull: true
  },
  username: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  password: {
    type: DataTypes.STRING,
    allowNull: true
  },
  auth_token: {
    type: DataTypes.STRING,
    allowNull: true
  },
  account_sid: {
    type: DataTypes.STRING,
    allowNull: true
  },
  from_number: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  webhook_url: {
    type: DataTypes.STRING,
    allowNull: true
  },
  delivery_reports: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  unicode_support: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  flash_sms: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  test_mode: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  is_default: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  rate_limit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'SMS per minute limit'
  },
  daily_limit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Daily SMS limit'
  },
  monthly_limit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Monthly SMS limit'
  },
  balance: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  sms_sent_today: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  sms_sent_month: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  last_reset_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  configuration: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  templates: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  auto_notifications: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {
      service_created: false,
      service_completed: false,
      payment_received: false,
      invoice_sent: false,
      reminder_due: false
    }
  },
  notification_settings: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  error_log: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  last_error: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  last_success_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  last_error_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'sms_settings',
  indexes: [
    {
      fields: ['company_id']
    },
    {
      fields: ['provider']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['is_default']
    }
  ]
});

module.exports = SmsSetting;
