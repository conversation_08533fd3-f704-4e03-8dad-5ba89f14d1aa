const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const PaymentOut = sequelize.define('PaymentOut', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  payment_number: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  supplier_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'suppliers',
      key: 'id'
    }
  },
  employee_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  payment_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  payment_method: {
    type: DataTypes.ENUM('cash', 'bank_transfer', 'cheque', 'credit_card', 'debit_card', 'upi', 'wallet', 'other'),
    allowNull: false
  },
  amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false
  },
  currency: {
    type: DataTypes.STRING(3),
    allowNull: false,
    defaultValue: 'INR'
  },
  exchange_rate: {
    type: DataTypes.DECIMAL(10, 4),
    allowNull: false,
    defaultValue: 1
  },
  reference_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  bank_name: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  cheque_number: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  cheque_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  transaction_id: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('pending', 'completed', 'failed', 'cancelled'),
    defaultValue: 'completed'
  },
  payment_type: {
    type: DataTypes.ENUM('purchase_payment', 'expense', 'salary', 'advance', 'refund', 'adjustment'),
    defaultValue: 'purchase_payment'
  },
  payee_name: {
    type: DataTypes.STRING,
    allowNull: true
  },
  purpose: {
    type: DataTypes.STRING,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  attachment: {
    type: DataTypes.STRING,
    allowNull: true
  },
  is_reconciled: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  reconciled_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'payments_out',
  paranoid: true,
  indexes: [
    {
      fields: ['payment_number']
    },
    {
      fields: ['company_id']
    },
    {
      fields: ['supplier_id']
    },
    {
      fields: ['employee_id']
    },
    {
      fields: ['payment_date']
    },
    {
      fields: ['payment_method']
    },
    {
      fields: ['status']
    },
    {
      fields: ['payment_type']
    },
    {
      fields: ['is_reconciled']
    }
  ],
  hooks: {
    beforeCreate: async (payment) => {
      if (!payment.payment_number) {
        const count = await PaymentOut.count({ where: { company_id: payment.company_id } });
        payment.payment_number = `POUT-${payment.company_id}-${String(count + 1).padStart(6, '0')}`;
      }
    }
  }
});

module.exports = PaymentOut;
