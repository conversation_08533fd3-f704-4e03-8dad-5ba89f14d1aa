const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const CustomerCategory = sequelize.define('CustomerCategory', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  category_name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  category_code: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  color: {
    type: DataTypes.STRING(7),
    allowNull: true,
    defaultValue: '#007bff'
  },
  icon: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  discount_percentage: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    defaultValue: 0,
    validate: {
      min: 0,
      max: 100
    }
  },
  credit_limit: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0
  },
  payment_terms: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 30,
    comment: 'Payment terms in days'
  },
  priority_level: {
    type: DataTypes.ENUM('low', 'normal', 'high', 'vip'),
    defaultValue: 'normal'
  },
  auto_assign_sales_rep: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  auto_assign_service_rep: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  notification_preferences: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {
      email: true,
      sms: false,
      whatsapp: false
    }
  },
  service_preferences: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  billing_preferences: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  custom_fields: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  is_default: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  customer_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'customer_categories',
  paranoid: true,
  indexes: [
    {
      unique: true,
      fields: ['company_id', 'category_name']
    },
    {
      fields: ['company_id']
    },
    {
      fields: ['category_code']
    },
    {
      fields: ['priority_level']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['is_default']
    },
    {
      fields: ['sort_order']
    }
  ]
});

module.exports = CustomerCategory;
