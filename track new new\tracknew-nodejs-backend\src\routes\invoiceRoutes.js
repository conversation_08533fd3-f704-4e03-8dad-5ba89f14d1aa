const express = require('express');
const { body } = require('express-validator');

const invoiceController = require('../controllers/invoiceController');
const { protect, restrictTo, restrictToCompany } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Protect all routes
router.use(protect);
router.use(restrictToCompany);

// Validation rules
const createInvoiceValidation = [
  body('customer_id')
    .isInt({ min: 1 })
    .withMessage('Customer ID is required and must be a valid number'),
  body('invoice_date')
    .optional()
    .isISO8601()
    .withMessage('Invoice date must be a valid date'),
  body('due_date')
    .optional()
    .isISO8601()
    .withMessage('Due date must be a valid date'),
  body('invoice_type')
    .optional()
    .isIn(['sales', 'service', 'proforma', 'credit_note', 'debit_note'])
    .withMessage('Invalid invoice type'),
  body('items')
    .isArray({ min: 1 })
    .withMessage('At least one item is required'),
  body('items.*.item_name')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Item name is required and must be less than 255 characters'),
  body('items.*.quantity')
    .isFloat({ min: 0.001 })
    .withMessage('Quantity must be greater than 0'),
  body('items.*.unit_price')
    .isFloat({ min: 0 })
    .withMessage('Unit price must be a positive number'),
  body('items.*.discount_type')
    .optional()
    .isIn(['percentage', 'fixed'])
    .withMessage('Discount type must be percentage or fixed'),
  body('items.*.discount_value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Discount value must be a positive number'),
  body('items.*.cgst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('CGST rate must be between 0 and 100'),
  body('items.*.sgst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('SGST rate must be between 0 and 100'),
  body('items.*.igst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('IGST rate must be between 0 and 100'),
  body('discount_type')
    .optional()
    .isIn(['percentage', 'fixed'])
    .withMessage('Discount type must be percentage or fixed'),
  body('discount_value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Discount value must be a positive number'),
  body('shipping_amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Shipping amount must be a positive number'),
  body('adjustment_amount')
    .optional()
    .isFloat()
    .withMessage('Adjustment amount must be a valid number'),
  body('payment_terms')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Payment terms must be less than 100 characters'),
  body('po_number')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('PO number must be less than 100 characters')
];

const updateInvoiceValidation = [
  body('due_date')
    .optional()
    .isISO8601()
    .withMessage('Due date must be a valid date'),
  body('status')
    .optional()
    .isIn(['draft', 'sent', 'paid', 'partially_paid', 'overdue', 'cancelled'])
    .withMessage('Invalid status value'),
  body('payment_status')
    .optional()
    .isIn(['unpaid', 'partially_paid', 'paid', 'overpaid'])
    .withMessage('Invalid payment status value'),
  body('adjustment_amount')
    .optional()
    .isFloat()
    .withMessage('Adjustment amount must be a valid number'),
  body('payment_terms')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Payment terms must be less than 100 characters'),
  body('po_number')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('PO number must be less than 100 characters'),
  body('terms_conditions')
    .optional()
    .trim()
    .isLength({ max: 5000 })
    .withMessage('Terms and conditions must be less than 5000 characters'),
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Notes must be less than 2000 characters'),
  body('internal_notes')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Internal notes must be less than 2000 characters')
];

// Routes
router
  .route('/')
  .get(invoiceController.getInvoices)
  .post(createInvoiceValidation, validateRequest, invoiceController.createInvoice);

router
  .route('/:id')
  .get(invoiceController.getInvoice)
  .put(updateInvoiceValidation, validateRequest, invoiceController.updateInvoice)
  .delete(restrictTo('admin', 'sub_admin'), invoiceController.deleteInvoice);

module.exports = router;
