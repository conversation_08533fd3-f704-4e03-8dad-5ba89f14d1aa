const { Proforma, Proforma<PERSON>tem, Customer, Company, User, Product, Estimation } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all proformas with filtering and pagination
const getProformas = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    status,
    customer_id,
    search,
    sort_by = 'created_at',
    sort_order = 'DESC',
    date_from,
    date_to
  } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = { company_id: req.user.company_id };

  // Apply filters
  if (status) {
    whereClause.status = status;
  }

  if (customer_id) {
    whereClause.customer_id = customer_id;
  }

  if (date_from && date_to) {
    whereClause.proforma_date = {
      [Op.between]: [new Date(date_from), new Date(date_to)]
    };
  }

  if (search) {
    whereClause[Op.or] = [
      { proforma_number: { [Op.like]: `%${search}%` } },
      { reference_number: { [Op.like]: `%${search}%` } },
      { notes: { [Op.like]: `%${search}%` } }
    ];
  }

  const { count, rows: proformas } = await Proforma.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'customer_code', 'email', 'mobile_number']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: ProformaItem,
        as: 'items',
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'product_name', 'product_code', 'unit']
          }
        ]
      }
    ],
    order: [[sort_by, sort_order.toUpperCase()]],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.status(200).json({
    status: 'success',
    data: {
      proformas,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: count,
        total_pages: Math.ceil(count / limit)
      }
    }
  });
});

// Get proforma by ID
const getProforma = catchAsync(async (req, res, next) => {
  const proforma = await Proforma.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    },
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: Company,
        as: 'company'
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: Estimation,
        as: 'estimation',
        attributes: ['id', 'estimation_number', 'status']
      },
      {
        model: ProformaItem,
        as: 'items',
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'product_name', 'product_code', 'unit', 'hsn_code']
          }
        ],
        order: [['sort_order', 'ASC']]
      }
    ]
  });

  if (!proforma) {
    return next(new AppError('Proforma not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      proforma
    }
  });
});

// Create new proforma
const createProforma = catchAsync(async (req, res, next) => {
  const {
    customer_id,
    estimation_id,
    proforma_date = new Date(),
    valid_until,
    items = [],
    discount_type,
    discount_value = 0,
    shipping_amount = 0,
    terms_conditions,
    notes,
    internal_notes,
    billing_address,
    shipping_address,
    payment_terms,
    delivery_terms,
    reference_number
  } = req.body;

  // Validate customer exists
  const customer = await Customer.findOne({
    where: { id: customer_id, company_id: req.user.company_id }
  });

  if (!customer) {
    return next(new AppError('Customer not found', 404));
  }

  // Calculate totals
  let subtotal = 0;
  const processedItems = items.map((item, index) => {
    const lineTotal = item.quantity * item.unit_price;
    const itemDiscountAmount = item.discount_type === 'percentage' 
      ? (lineTotal * item.discount_value) / 100 
      : (item.discount_value || 0);
    const taxableAmount = lineTotal - itemDiscountAmount;
    
    const cgstAmount = (taxableAmount * (item.cgst_rate || 0)) / 100;
    const sgstAmount = (taxableAmount * (item.sgst_rate || 0)) / 100;
    const igstAmount = (taxableAmount * (item.igst_rate || 0)) / 100;
    const cessAmount = (taxableAmount * (item.cess_rate || 0)) / 100;
    
    const taxAmount = cgstAmount + sgstAmount + igstAmount + cessAmount;
    const totalAmount = taxableAmount + taxAmount;
    
    subtotal += totalAmount;
    
    return {
      ...item,
      discount_amount: itemDiscountAmount,
      taxable_amount: taxableAmount,
      cgst_amount: cgstAmount,
      sgst_amount: sgstAmount,
      igst_amount: igstAmount,
      cess_amount: cessAmount,
      tax_amount: taxAmount,
      total_amount: totalAmount,
      sort_order: index + 1
    };
  });

  // Calculate proforma level discount
  const discountAmount = discount_type === 'percentage' 
    ? (subtotal * discount_value) / 100 
    : (discount_value || 0);

  const taxAmount = processedItems.reduce((sum, item) => sum + item.tax_amount, 0);
  const totalAmount = subtotal - discountAmount + parseFloat(shipping_amount || 0);

  // Create proforma
  const proforma = await Proforma.create({
    company_id: req.user.company_id,
    customer_id,
    estimation_id,
    proforma_date,
    valid_until,
    subtotal,
    discount_type,
    discount_value,
    discount_amount,
    tax_amount: taxAmount,
    shipping_amount,
    total_amount,
    terms_conditions,
    notes,
    internal_notes,
    billing_address,
    shipping_address,
    payment_terms,
    delivery_terms,
    reference_number,
    created_by: req.user.id
  });

  // Create proforma items
  if (processedItems.length > 0) {
    const itemsToCreate = processedItems.map(item => ({
      ...item,
      proforma_id: proforma.id
    }));
    
    await ProformaItem.bulkCreate(itemsToCreate);
  }

  // Fetch created proforma with associations
  const createdProforma = await Proforma.findByPk(proforma.id, {
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: ProformaItem,
        as: 'items',
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'product_name', 'product_code', 'unit']
          }
        ]
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      proforma: createdProforma
    }
  });
});

// Update proforma
const updateProforma = catchAsync(async (req, res, next) => {
  const proforma = await Proforma.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!proforma) {
    return next(new AppError('Proforma not found', 404));
  }

  // Check if proforma can be updated
  if (proforma.status === 'converted') {
    return next(new AppError('Cannot update converted proforma', 400));
  }

  const allowedUpdates = [
    'valid_until', 'status', 'terms_conditions', 'notes', 'internal_notes',
    'billing_address', 'shipping_address', 'payment_terms', 'delivery_terms',
    'reference_number'
  ];

  const updates = {};
  allowedUpdates.forEach(field => {
    if (req.body[field] !== undefined) {
      updates[field] = req.body[field];
    }
  });

  updates.updated_by = req.user.id;

  await proforma.update(updates);

  const updatedProforma = await Proforma.findByPk(proforma.id, {
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: ProformaItem,
        as: 'items'
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      proforma: updatedProforma
    }
  });
});

// Delete proforma
const deleteProforma = catchAsync(async (req, res, next) => {
  const proforma = await Proforma.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!proforma) {
    return next(new AppError('Proforma not found', 404));
  }

  if (proforma.status === 'converted') {
    return next(new AppError('Cannot delete converted proforma', 400));
  }

  await proforma.destroy();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

module.exports = {
  getProformas,
  getProforma,
  createProforma,
  updateProforma,
  deleteProforma
};
