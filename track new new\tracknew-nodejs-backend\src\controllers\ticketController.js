const { Tickets, Customer, User, Service, Product, Sales } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all tickets with filtering and pagination
const getTickets = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    status,
    priority,
    ticket_type,
    assigned_to,
    customer_id,
    search,
    sort_by = 'created_at',
    sort_order = 'DESC',
    date_from,
    date_to
  } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = { company_id: req.user.company_id };

  // Apply filters
  if (status) {
    whereClause.status = status;
  }

  if (priority) {
    whereClause.priority = priority;
  }

  if (ticket_type) {
    whereClause.ticket_type = ticket_type;
  }

  if (assigned_to) {
    whereClause.assigned_to = assigned_to;
  }

  if (customer_id) {
    whereClause.customer_id = customer_id;
  }

  if (date_from && date_to) {
    whereClause.createdAt = {
      [Op.between]: [new Date(date_from), new Date(date_to)]
    };
  }

  if (search) {
    whereClause[Op.or] = [
      { ticket_number: { [Op.like]: `%${search}%` } },
      { subject: { [Op.like]: `%${search}%` } },
      { description: { [Op.like]: `%${search}%` } },
      { customer_name: { [Op.like]: `%${search}%` } },
      { customer_email: { [Op.like]: `%${search}%` } }
    ];
  }

  const { count, rows: tickets } = await Tickets.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'customer_code', 'email', 'mobile_number']
      },
      {
        model: User,
        as: 'assignedTo',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      }
    ],
    order: [[sort_by, sort_order.toUpperCase()]],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.status(200).json({
    status: 'success',
    data: {
      tickets,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: count,
        total_pages: Math.ceil(count / limit)
      }
    }
  });
});

// Get ticket by ID
const getTicket = catchAsync(async (req, res, next) => {
  const ticket = await Tickets.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    },
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: User,
        as: 'assignedTo',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'escalatedTo',
        attributes: ['id', 'name', 'email']
      },
      {
        model: Service,
        as: 'relatedService',
        attributes: ['id', 'service_code', 'status']
      },
      {
        model: Product,
        as: 'relatedProduct',
        attributes: ['id', 'product_name', 'product_code']
      },
      {
        model: Sales,
        as: 'relatedOrder',
        attributes: ['id', 'sales_number', 'status']
      }
    ]
  });

  if (!ticket) {
    return next(new AppError('Ticket not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      ticket
    }
  });
});

// Create new ticket
const createTicket = catchAsync(async (req, res, next) => {
  const {
    customer_id,
    subject,
    description,
    ticket_type = 'support',
    priority = 'medium',
    category,
    subcategory,
    assigned_to,
    assigned_team,
    source = 'web',
    customer_name,
    customer_email,
    customer_phone,
    due_date,
    tags = [],
    attachments = [],
    related_service_id,
    related_product_id,
    related_order_id,
    internal_notes
  } = req.body;

  // If customer_id is provided, validate it exists
  if (customer_id) {
    const customer = await Customer.findOne({
      where: { id: customer_id, company_id: req.user.company_id }
    });

    if (!customer) {
      return next(new AppError('Customer not found', 404));
    }
  }

  // If assigned_to is provided, validate user exists
  if (assigned_to) {
    const assignee = await User.findOne({
      where: { id: assigned_to, company_id: req.user.company_id }
    });

    if (!assignee) {
      return next(new AppError('Assigned user not found', 404));
    }
  }

  const ticket = await Tickets.create({
    company_id: req.user.company_id,
    customer_id,
    user_id: req.user.id,
    subject,
    description,
    ticket_type,
    priority,
    category,
    subcategory,
    assigned_to,
    assigned_team,
    source,
    customer_name,
    customer_email,
    customer_phone,
    due_date,
    tags,
    attachments,
    related_service_id,
    related_product_id,
    related_order_id,
    internal_notes,
    created_by: req.user.id
  });

  // Fetch created ticket with associations
  const createdTicket = await Tickets.findByPk(ticket.id, {
    include: [
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'customer_code', 'email']
      },
      {
        model: User,
        as: 'assignedTo',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      ticket: createdTicket
    }
  });
});

// Update ticket
const updateTicket = catchAsync(async (req, res, next) => {
  const ticket = await Tickets.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!ticket) {
    return next(new AppError('Ticket not found', 404));
  }

  const allowedUpdates = [
    'subject', 'description', 'ticket_type', 'priority', 'status', 'category',
    'subcategory', 'assigned_to', 'assigned_team', 'customer_name', 'customer_email',
    'customer_phone', 'due_date', 'resolution_date', 'satisfaction_rating',
    'satisfaction_feedback', 'tags', 'attachments', 'escalation_level',
    'escalated_to', 'escalated_at', 'sla_breach', 'internal_notes',
    'resolution_summary', 'follow_up_required', 'follow_up_date',
    'is_billable', 'billable_hours', 'hourly_rate', 'total_cost'
  ];

  const updates = {};
  allowedUpdates.forEach(field => {
    if (req.body[field] !== undefined) {
      updates[field] = req.body[field];
    }
  });

  // Auto-set resolution date when status changes to resolved/closed
  if (updates.status && ['resolved', 'closed'].includes(updates.status) && !ticket.resolution_date) {
    updates.resolution_date = new Date();
  }

  // Calculate resolution time if resolving
  if (updates.resolution_date && !ticket.resolution_date) {
    const resolutionTime = Math.floor((new Date(updates.resolution_date) - new Date(ticket.createdAt)) / (1000 * 60));
    updates.resolution_time = resolutionTime;
  }

  // Calculate total cost if billable
  if (updates.billable_hours && updates.hourly_rate) {
    updates.total_cost = parseFloat(updates.billable_hours) * parseFloat(updates.hourly_rate);
  }

  updates.updated_by = req.user.id;

  await ticket.update(updates);

  const updatedTicket = await Tickets.findByPk(ticket.id, {
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: User,
        as: 'assignedTo',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      ticket: updatedTicket
    }
  });
});

// Delete ticket
const deleteTicket = catchAsync(async (req, res, next) => {
  const ticket = await Tickets.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!ticket) {
    return next(new AppError('Ticket not found', 404));
  }

  // Only allow deletion of open tickets
  if (!['open', 'cancelled'].includes(ticket.status)) {
    return next(new AppError('Cannot delete ticket that is in progress or resolved', 400));
  }

  await ticket.destroy();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

// Get ticket statistics
const getTicketStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  const totalTickets = await Tickets.count({
    where: { company_id: companyId }
  });

  const statusStats = await Tickets.findAll({
    where: { company_id: companyId },
    attributes: [
      'status',
      [Tickets.sequelize.fn('COUNT', Tickets.sequelize.col('id')), 'count']
    ],
    group: ['status'],
    raw: true
  });

  const priorityStats = await Tickets.findAll({
    where: { company_id: companyId },
    attributes: [
      'priority',
      [Tickets.sequelize.fn('COUNT', Tickets.sequelize.col('id')), 'count']
    ],
    group: ['priority'],
    raw: true
  });

  const typeStats = await Tickets.findAll({
    where: { company_id: companyId },
    attributes: [
      'ticket_type',
      [Tickets.sequelize.fn('COUNT', Tickets.sequelize.col('id')), 'count']
    ],
    group: ['ticket_type'],
    raw: true
  });

  // Average resolution time
  const avgResolutionTime = await Tickets.findOne({
    where: { 
      company_id: companyId,
      resolution_time: { [Op.not]: null }
    },
    attributes: [
      [Tickets.sequelize.fn('AVG', Tickets.sequelize.col('resolution_time')), 'avg_resolution_time']
    ],
    raw: true
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_tickets: totalTickets,
      status_stats: statusStats,
      priority_stats: priorityStats,
      type_stats: typeStats,
      avg_resolution_time: avgResolutionTime?.avg_resolution_time || 0
    }
  });
});

module.exports = {
  getTickets,
  getTicket,
  createTicket,
  updateTicket,
  deleteTicket,
  getTicketStats
};
