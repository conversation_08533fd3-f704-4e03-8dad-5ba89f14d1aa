const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const WebsiteTemplates = sequelize.define('WebsiteTemplates', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  thumbnail: {
    type: DataTypes.STRING,
    allowNull: true
  },
  preview_url: {
    type: DataTypes.STRING,
    allowNull: true
  },
  path: {
    type: DataTypes.STRING,
    allowNull: false
  },
  layout: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  color_scheme: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  font_family: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  theme_settings: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  header_items: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  footer_items: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  supported_pages: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  features: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  tag_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'template_tags',
      key: 'id'
    }
  },
  category: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  version: {
    type: DataTypes.STRING(20),
    allowNull: true,
    defaultValue: '1.0.0'
  },
  is_premium: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  is_responsive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  min_requirements: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  demo_data: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  installation_guide: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  changelog: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  download_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  rating: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    validate: {
      min: 0,
      max: 5
    }
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'website_templates',
  paranoid: true,
  indexes: [
    {
      fields: ['name']
    },
    {
      fields: ['tag_id']
    },
    {
      fields: ['category']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['is_premium']
    },
    {
      fields: ['rating']
    }
  ]
});

module.exports = WebsiteTemplates;
