const { Category, Product, User } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all categories with filtering and pagination
const getCategories = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    search,
    sort_by = 'created_at',
    sort_order = 'DESC',
    status,
    parent_id
  } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = { company_id: req.user.company_id };

  if (status !== undefined) {
    whereClause.status = status === 'true';
  }

  if (parent_id !== undefined) {
    whereClause.parent_id = parent_id === 'null' ? null : parent_id;
  }

  if (search) {
    whereClause[Op.or] = [
      { category_name: { [Op.like]: `%${search}%` } },
      { category_code: { [Op.like]: `%${search}%` } },
      { description: { [Op.like]: `%${search}%` } }
    ];
  }

  const { count, rows: categories } = await Category.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: Category,
        as: 'parent',
        attributes: ['id', 'category_name']
      },
      {
        model: Category,
        as: 'children',
        attributes: ['id', 'category_name', 'status']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      }
    ],
    order: [[sort_by, sort_order.toUpperCase()]],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.status(200).json({
    status: 'success',
    data: {
      categories,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: count,
        total_pages: Math.ceil(count / limit)
      }
    }
  });
});

// Get category by ID
const getCategory = catchAsync(async (req, res, next) => {
  const category = await Category.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    },
    include: [
      {
        model: Category,
        as: 'parent',
        attributes: ['id', 'category_name']
      },
      {
        model: Category,
        as: 'children',
        attributes: ['id', 'category_name', 'status']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  if (!category) {
    return next(new AppError('Category not found', 404));
  }

  // Get category's product count
  const productCount = await Product.count({
    where: { category_id: category.id }
  });

  res.status(200).json({
    status: 'success',
    data: {
      category,
      product_count: productCount
    }
  });
});

// Create new category
const createCategory = catchAsync(async (req, res, next) => {
  const {
    category_name,
    category_code,
    description,
    parent_id,
    image,
    icon,
    color,
    sort_order = 0,
    meta_title,
    meta_description,
    status = true
  } = req.body;

  // Check if category with same name or code already exists
  const existingCategory = await Category.findOne({
    where: {
      company_id: req.user.company_id,
      [Op.or]: [
        { category_name },
        category_code ? { category_code } : null
      ].filter(Boolean)
    }
  });

  if (existingCategory) {
    return next(new AppError('Category with this name or code already exists', 400));
  }

  // If parent_id is provided, validate it exists
  if (parent_id) {
    const parentCategory = await Category.findOne({
      where: { id: parent_id, company_id: req.user.company_id }
    });

    if (!parentCategory) {
      return next(new AppError('Parent category not found', 404));
    }
  }

  const category = await Category.create({
    company_id: req.user.company_id,
    category_name,
    category_code,
    description,
    parent_id,
    image,
    icon,
    color,
    sort_order,
    meta_title,
    meta_description,
    status,
    created_by: req.user.id
  });

  // Fetch created category with associations
  const createdCategory = await Category.findByPk(category.id, {
    include: [
      {
        model: Category,
        as: 'parent',
        attributes: ['id', 'category_name']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      category: createdCategory
    }
  });
});

// Update category
const updateCategory = catchAsync(async (req, res, next) => {
  const category = await Category.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!category) {
    return next(new AppError('Category not found', 404));
  }

  // Check for duplicate name or code if being updated
  if (req.body.category_name || req.body.category_code) {
    const existingCategory = await Category.findOne({
      where: {
        company_id: req.user.company_id,
        id: { [Op.ne]: category.id },
        [Op.or]: [
          req.body.category_name ? { category_name: req.body.category_name } : null,
          req.body.category_code ? { category_code: req.body.category_code } : null
        ].filter(Boolean)
      }
    });

    if (existingCategory) {
      return next(new AppError('Category with this name or code already exists', 400));
    }
  }

  // Validate parent_id if being updated
  if (req.body.parent_id) {
    // Cannot set self as parent
    if (req.body.parent_id === category.id) {
      return next(new AppError('Category cannot be its own parent', 400));
    }

    const parentCategory = await Category.findOne({
      where: { id: req.body.parent_id, company_id: req.user.company_id }
    });

    if (!parentCategory) {
      return next(new AppError('Parent category not found', 404));
    }
  }

  const allowedUpdates = [
    'category_name', 'category_code', 'description', 'parent_id', 'image',
    'icon', 'color', 'sort_order', 'meta_title', 'meta_description', 'status'
  ];

  const updates = {};
  allowedUpdates.forEach(field => {
    if (req.body[field] !== undefined) {
      updates[field] = req.body[field];
    }
  });

  updates.updated_by = req.user.id;

  await category.update(updates);

  const updatedCategory = await Category.findByPk(category.id, {
    include: [
      {
        model: Category,
        as: 'parent',
        attributes: ['id', 'category_name']
      },
      {
        model: Category,
        as: 'children',
        attributes: ['id', 'category_name', 'status']
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      category: updatedCategory
    }
  });
});

// Delete category
const deleteCategory = catchAsync(async (req, res, next) => {
  const category = await Category.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!category) {
    return next(new AppError('Category not found', 404));
  }

  // Check if category has any products
  const productCount = await Product.count({
    where: { category_id: category.id }
  });

  if (productCount > 0) {
    return next(new AppError('Cannot delete category with existing products', 400));
  }

  // Check if category has any child categories
  const childCount = await Category.count({
    where: { parent_id: category.id }
  });

  if (childCount > 0) {
    return next(new AppError('Cannot delete category with child categories', 400));
  }

  await category.destroy();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

// Get category tree
const getCategoryTree = catchAsync(async (req, res) => {
  const categories = await Category.findAll({
    where: { 
      company_id: req.user.company_id,
      status: true
    },
    include: [
      {
        model: Category,
        as: 'children',
        where: { status: true },
        required: false,
        include: [
          {
            model: Category,
            as: 'children',
            where: { status: true },
            required: false
          }
        ]
      }
    ],
    where: { parent_id: null },
    order: [
      ['sort_order', 'ASC'],
      ['category_name', 'ASC'],
      [{ model: Category, as: 'children' }, 'sort_order', 'ASC'],
      [{ model: Category, as: 'children' }, 'category_name', 'ASC']
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      categories
    }
  });
});

module.exports = {
  getCategories,
  getCategory,
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoryTree
};
