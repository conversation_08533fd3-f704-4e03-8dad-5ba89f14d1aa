const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const RMA = sequelize.define('RMA', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  rma_number: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  customer_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'customers',
      key: 'id'
    }
  },
  product_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  sales_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'sales',
      key: 'id'
    }
  },
  service_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'services',
      key: 'id'
    }
  },
  rma_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  rma_type: {
    type: DataTypes.ENUM('return', 'exchange', 'repair', 'warranty_claim'),
    allowNull: false
  },
  reason: {
    type: DataTypes.ENUM('defective', 'damaged', 'wrong_item', 'not_as_described', 'customer_request', 'warranty_issue', 'other'),
    allowNull: false
  },
  reason_description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  product_name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  product_code: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  serial_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  batch_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  quantity: {
    type: DataTypes.DECIMAL(10, 3),
    allowNull: false,
    defaultValue: 1
  },
  unit_price: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  total_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected', 'received', 'inspected', 'processed', 'completed', 'cancelled'),
    defaultValue: 'pending'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium'
  },
  expected_resolution_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  actual_resolution_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  received_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  inspection_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  inspection_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  resolution_type: {
    type: DataTypes.ENUM('refund', 'replacement', 'repair', 'credit_note', 'no_action'),
    allowNull: true
  },
  resolution_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  refund_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0
  },
  replacement_product_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  repair_cost: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0
  },
  shipping_cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0
  },
  warehouse_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'warehouses',
      key: 'id'
    }
  },
  assigned_to: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  customer_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  internal_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  is_warranty_valid: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  warranty_expiry_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'rmas',
  paranoid: true,
  indexes: [
    {
      fields: ['rma_number']
    },
    {
      fields: ['company_id']
    },
    {
      fields: ['customer_id']
    },
    {
      fields: ['product_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['rma_type']
    },
    {
      fields: ['reason']
    },
    {
      fields: ['rma_date']
    },
    {
      fields: ['assigned_to']
    },
    {
      fields: ['serial_number']
    }
  ],
  hooks: {
    beforeCreate: async (rma) => {
      if (!rma.rma_number) {
        const count = await RMA.count({ where: { company_id: rma.company_id } });
        rma.rma_number = `RMA-${rma.company_id}-${String(count + 1).padStart(6, '0')}`;
      }
    }
  }
});

module.exports = RMA;
