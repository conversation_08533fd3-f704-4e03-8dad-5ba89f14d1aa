const { Supplier, PurchaseOrder, Company, User } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all suppliers with filtering and pagination
const getSuppliers = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    supplier_type,
    supplier_category,
    search,
    sort_by = 'created_at',
    sort_order = 'DESC',
    status
  } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = { company_id: req.user.company_id };

  // Apply filters
  if (supplier_type) {
    whereClause.supplier_type = supplier_type;
  }

  if (supplier_category) {
    whereClause.supplier_category = supplier_category;
  }

  if (status !== undefined) {
    whereClause.status = status === 'true';
  }

  if (search) {
    whereClause[Op.or] = [
      { supplier_name: { [Op.like]: `%${search}%` } },
      { supplier_code: { [Op.like]: `%${search}%` } },
      { email: { [Op.like]: `%${search}%` } },
      { mobile_number: { [Op.like]: `%${search}%` } },
      { contact_person: { [Op.like]: `%${search}%` } }
    ];
  }

  const { count, rows: suppliers } = await Supplier.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      }
    ],
    order: [[sort_by, sort_order.toUpperCase()]],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.status(200).json({
    status: 'success',
    data: {
      suppliers,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: count,
        total_pages: Math.ceil(count / limit)
      }
    }
  });
});

// Get supplier by ID
const getSupplier = catchAsync(async (req, res, next) => {
  const supplier = await Supplier.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    },
    include: [
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  if (!supplier) {
    return next(new AppError('Supplier not found', 404));
  }

  // Get supplier's purchase order statistics
  const purchaseStats = await PurchaseOrder.findAll({
    where: { supplier_id: supplier.id },
    attributes: [
      'status',
      [PurchaseOrder.sequelize.fn('COUNT', PurchaseOrder.sequelize.col('id')), 'count'],
      [PurchaseOrder.sequelize.fn('SUM', PurchaseOrder.sequelize.col('total_amount')), 'total_amount']
    ],
    group: ['status'],
    raw: true
  });

  // Get recent purchase orders
  const recentPurchaseOrders = await PurchaseOrder.findAll({
    where: { supplier_id: supplier.id },
    order: [['created_at', 'DESC']],
    limit: 5,
    attributes: ['id', 'po_number', 'status', 'total_amount', 'created_at']
  });

  res.status(200).json({
    status: 'success',
    data: {
      supplier,
      purchase_stats: purchaseStats,
      recent_purchase_orders: recentPurchaseOrders
    }
  });
});

// Create new supplier
const createSupplier = catchAsync(async (req, res, next) => {
  const {
    supplier_name,
    supplier_type = 'business',
    contact_person,
    email,
    mobile_number,
    phone_number,
    fax_number,
    website,
    gst_number,
    pan_number,
    tan_number,
    registration_number,
    billing_address,
    billing_city,
    billing_state,
    billing_country = 'India',
    billing_pincode,
    shipping_address,
    shipping_city,
    shipping_state,
    shipping_country = 'India',
    shipping_pincode,
    bank_name,
    bank_account_number,
    bank_ifsc_code,
    bank_branch,
    payment_terms = 30,
    credit_limit = 0,
    opening_balance = 0,
    supplier_category = 'regular',
    rating,
    notes,
    tags = []
  } = req.body;

  // Check if supplier with same email or mobile already exists
  if (email || mobile_number) {
    const existingSupplier = await Supplier.findOne({
      where: {
        company_id: req.user.company_id,
        [Op.or]: [
          email ? { email } : null,
          mobile_number ? { mobile_number } : null
        ].filter(Boolean)
      }
    });

    if (existingSupplier) {
      return next(new AppError('Supplier with this email or mobile number already exists', 400));
    }
  }

  const supplier = await Supplier.create({
    company_id: req.user.company_id,
    supplier_name,
    supplier_type,
    contact_person,
    email,
    mobile_number,
    phone_number,
    fax_number,
    website,
    gst_number,
    pan_number,
    tan_number,
    registration_number,
    billing_address,
    billing_city,
    billing_state,
    billing_country,
    billing_pincode,
    shipping_address,
    shipping_city,
    shipping_state,
    shipping_country,
    shipping_pincode,
    bank_name,
    bank_account_number,
    bank_ifsc_code,
    bank_branch,
    payment_terms,
    credit_limit,
    opening_balance,
    current_balance: opening_balance,
    supplier_category,
    rating,
    notes,
    tags,
    created_by: req.user.id
  });

  // Fetch the created supplier with associations
  const createdSupplier = await Supplier.findByPk(supplier.id, {
    include: [
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      supplier: createdSupplier
    }
  });
});

// Update supplier
const updateSupplier = catchAsync(async (req, res, next) => {
  const supplier = await Supplier.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!supplier) {
    return next(new AppError('Supplier not found', 404));
  }

  // Check for duplicate email or mobile if being updated
  if (req.body.email || req.body.mobile_number) {
    const existingSupplier = await Supplier.findOne({
      where: {
        company_id: req.user.company_id,
        id: { [Op.ne]: supplier.id },
        [Op.or]: [
          req.body.email ? { email: req.body.email } : null,
          req.body.mobile_number ? { mobile_number: req.body.mobile_number } : null
        ].filter(Boolean)
      }
    });

    if (existingSupplier) {
      return next(new AppError('Supplier with this email or mobile number already exists', 400));
    }
  }

  const allowedUpdates = [
    'supplier_name', 'supplier_type', 'contact_person', 'email', 'mobile_number',
    'phone_number', 'fax_number', 'website', 'gst_number', 'pan_number', 'tan_number',
    'registration_number', 'billing_address', 'billing_city', 'billing_state',
    'billing_country', 'billing_pincode', 'shipping_address', 'shipping_city',
    'shipping_state', 'shipping_country', 'shipping_pincode', 'bank_name',
    'bank_account_number', 'bank_ifsc_code', 'bank_branch', 'payment_terms',
    'credit_limit', 'supplier_category', 'rating', 'status', 'notes', 'tags'
  ];

  const updates = {};
  allowedUpdates.forEach(field => {
    if (req.body[field] !== undefined) {
      updates[field] = req.body[field];
    }
  });

  updates.updated_by = req.user.id;

  await supplier.update(updates);

  // Fetch updated supplier with associations
  const updatedSupplier = await Supplier.findByPk(supplier.id, {
    include: [
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      supplier: updatedSupplier
    }
  });
});

// Delete supplier
const deleteSupplier = catchAsync(async (req, res, next) => {
  const supplier = await Supplier.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!supplier) {
    return next(new AppError('Supplier not found', 404));
  }

  // Check if supplier has any purchase orders
  const purchaseOrderCount = await PurchaseOrder.count({
    where: { supplier_id: supplier.id }
  });

  if (purchaseOrderCount > 0) {
    return next(new AppError('Cannot delete supplier with existing purchase orders', 400));
  }

  await supplier.destroy();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

// Get supplier statistics
const getSupplierStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  const totalSuppliers = await Supplier.count({
    where: { company_id: companyId }
  });

  const activeSuppliers = await Supplier.count({
    where: { company_id: companyId, status: true }
  });

  const typeStats = await Supplier.findAll({
    where: { company_id: companyId },
    attributes: [
      'supplier_type',
      [Supplier.sequelize.fn('COUNT', Supplier.sequelize.col('id')), 'count']
    ],
    group: ['supplier_type'],
    raw: true
  });

  const categoryStats = await Supplier.findAll({
    where: { company_id: companyId },
    attributes: [
      'supplier_category',
      [Supplier.sequelize.fn('COUNT', Supplier.sequelize.col('id')), 'count']
    ],
    group: ['supplier_category'],
    raw: true
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_suppliers: totalSuppliers,
      active_suppliers: activeSuppliers,
      type_stats: typeStats,
      category_stats: categoryStats
    }
  });
});

module.exports = {
  getSuppliers,
  getSupplier,
  createSupplier,
  updateSupplier,
  deleteSupplier,
  getSupplierStats
};
