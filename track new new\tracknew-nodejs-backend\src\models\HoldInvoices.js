const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const HoldInvoices = sequelize.define('HoldInvoices', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  customer_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'customers',
      key: 'id'
    }
  },
  hold_reference: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  invoice_type: {
    type: DataTypes.ENUM('sales', 'service', 'proforma', 'estimate'),
    defaultValue: 'sales'
  },
  invoice_data: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: {}
  },
  items_data: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: []
  },
  customer_data: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  billing_address: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  shipping_address: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  subtotal: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  discount_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  tax_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  shipping_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0
  },
  total_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  currency: {
    type: DataTypes.STRING(3),
    allowNull: false,
    defaultValue: 'INR'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  internal_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  terms_conditions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  payment_terms: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  due_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  valid_until: {
    type: DataTypes.DATE,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('draft', 'hold', 'converted', 'expired', 'cancelled'),
    defaultValue: 'hold'
  },
  hold_reason: {
    type: DataTypes.STRING,
    allowNull: true
  },
  converted_to_type: {
    type: DataTypes.ENUM('invoice', 'proforma', 'estimate'),
    allowNull: true
  },
  converted_to_id: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  converted_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  template_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'invoice_templates',
      key: 'id'
    }
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'hold_invoices',
  paranoid: true,
  indexes: [
    {
      unique: true,
      fields: ['hold_reference']
    },
    {
      fields: ['company_id']
    },
    {
      fields: ['customer_id']
    },
    {
      fields: ['invoice_type']
    },
    {
      fields: ['status']
    },
    {
      fields: ['converted_to_type', 'converted_to_id']
    },
    {
      fields: ['expires_at']
    }
  ],
  hooks: {
    beforeCreate: async (holdInvoice) => {
      if (!holdInvoice.hold_reference) {
        const count = await HoldInvoices.count({ where: { company_id: holdInvoice.company_id } });
        holdInvoice.hold_reference = `HOLD-${holdInvoice.company_id}-${String(count + 1).padStart(6, '0')}`;
      }
    }
  }
});

module.exports = HoldInvoices;
