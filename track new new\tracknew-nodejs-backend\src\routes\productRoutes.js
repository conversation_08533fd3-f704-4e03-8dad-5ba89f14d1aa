const express = require('express');
const { body } = require('express-validator');

const productController = require('../controllers/productController');
const { protect, restrictTo, restrictToCompany } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Protect all routes
router.use(protect);
router.use(restrictToCompany);

// Validation rules
const createProductValidation = [
  body('product_name')
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('Product name is required and must be between 2-255 characters'),
  body('product_code')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Product code is required and must be less than 100 characters'),
  body('product_type')
    .optional()
    .isIn(['physical', 'digital', 'service'])
    .withMessage('Product type must be physical, digital, or service'),
  body('purchase_price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Purchase price must be a positive number'),
  body('selling_price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Selling price must be a positive number'),
  body('tax_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Tax rate must be between 0 and 100')
];

const updateProductValidation = [
  body('product_name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('Product name must be between 2-255 characters'),
  body('product_code')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Product code must be less than 100 characters'),
  body('status')
    .optional()
    .isBoolean()
    .withMessage('Status must be a boolean value')
];

// Routes
router
  .route('/')
  .get(productController.getProducts)
  .post(createProductValidation, validateRequest, productController.createProduct);

router
  .route('/:id')
  .get(productController.getProduct)
  .put(updateProductValidation, validateRequest, productController.updateProduct)
  .delete(restrictTo('admin', 'sub_admin'), productController.deleteProduct);

module.exports = router;
