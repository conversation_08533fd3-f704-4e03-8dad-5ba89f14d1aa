const { Product, Brand, Category, Unit, Company, User } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all products with filtering and pagination
const getProducts = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    category_id,
    brand_id,
    unit_id,
    product_type,
    status,
    search,
    sort_by = 'created_at',
    sort_order = 'DESC'
  } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = { company_id: req.user.company_id };

  // Apply filters
  if (category_id) {
    whereClause.category_id = category_id;
  }

  if (brand_id) {
    whereClause.brand_id = brand_id;
  }

  if (unit_id) {
    whereClause.unit_id = unit_id;
  }

  if (product_type) {
    whereClause.product_type = product_type;
  }

  if (status !== undefined) {
    whereClause.status = status === 'true';
  }

  if (search) {
    whereClause[Op.or] = [
      { product_name: { [Op.like]: `%${search}%` } },
      { product_code: { [Op.like]: `%${search}%` } },
      { sku: { [Op.like]: `%${search}%` } },
      { barcode: { [Op.like]: `%${search}%` } },
      { description: { [Op.like]: `%${search}%` } }
    ];
  }

  const { count, rows: products } = await Product.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: Brand,
        as: 'brand',
        attributes: ['id', 'brand_name']
      },
      {
        model: Category,
        as: 'category',
        attributes: ['id', 'category_name']
      },
      {
        model: Unit,
        as: 'unitDetails',
        attributes: ['id', 'unit_name', 'unit_symbol']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      }
    ],
    order: [[sort_by, sort_order.toUpperCase()]],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.status(200).json({
    status: 'success',
    data: {
      products,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: count,
        total_pages: Math.ceil(count / limit)
      }
    }
  });
});

// Get product by ID
const getProduct = catchAsync(async (req, res, next) => {
  const product = await Product.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    },
    include: [
      {
        model: Brand,
        as: 'brand'
      },
      {
        model: Category,
        as: 'category'
      },
      {
        model: Unit,
        as: 'unitDetails'
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  if (!product) {
    return next(new AppError('Product not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      product
    }
  });
});

// Create new product
const createProduct = catchAsync(async (req, res, next) => {
  const {
    product_name,
    product_code,
    sku,
    barcode,
    description,
    product_type = 'physical',
    category_id,
    brand_id,
    unit_id,
    unit,
    purchase_price = 0,
    selling_price = 0,
    mrp = 0,
    tax_rate = 0,
    hsn_code,
    weight,
    dimensions,
    min_stock_level = 0,
    max_stock_level = 0,
    reorder_level = 0,
    current_stock = 0,
    track_inventory = true,
    allow_negative_stock = false,
    warranty_period,
    warranty_type,
    images = [],
    tags = [],
    custom_fields = {},
    status = true
  } = req.body;

  // Check if product with same code/SKU already exists
  const existingProduct = await Product.findOne({
    where: {
      company_id: req.user.company_id,
      [Op.or]: [
        { product_code },
        sku ? { sku } : null,
        barcode ? { barcode } : null
      ].filter(Boolean)
    }
  });

  if (existingProduct) {
    return next(new AppError('Product with this code, SKU, or barcode already exists', 400));
  }

  const product = await Product.create({
    company_id: req.user.company_id,
    product_name,
    product_code,
    sku,
    barcode,
    description,
    product_type,
    category_id,
    brand_id,
    unit_id,
    unit,
    purchase_price,
    selling_price,
    mrp,
    tax_rate,
    hsn_code,
    weight,
    dimensions,
    min_stock_level,
    max_stock_level,
    reorder_level,
    current_stock,
    track_inventory,
    allow_negative_stock,
    warranty_period,
    warranty_type,
    images,
    tags,
    custom_fields,
    status,
    created_by: req.user.id
  });

  // Fetch created product with associations
  const createdProduct = await Product.findByPk(product.id, {
    include: [
      {
        model: Brand,
        as: 'brand',
        attributes: ['id', 'brand_name']
      },
      {
        model: Category,
        as: 'category',
        attributes: ['id', 'category_name']
      },
      {
        model: Unit,
        as: 'unitDetails',
        attributes: ['id', 'unit_name', 'unit_symbol']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      product: createdProduct
    }
  });
});

// Update product
const updateProduct = catchAsync(async (req, res, next) => {
  const product = await Product.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!product) {
    return next(new AppError('Product not found', 404));
  }

  // Check for duplicate codes if being updated
  if (req.body.product_code || req.body.sku || req.body.barcode) {
    const existingProduct = await Product.findOne({
      where: {
        company_id: req.user.company_id,
        id: { [Op.ne]: product.id },
        [Op.or]: [
          req.body.product_code ? { product_code: req.body.product_code } : null,
          req.body.sku ? { sku: req.body.sku } : null,
          req.body.barcode ? { barcode: req.body.barcode } : null
        ].filter(Boolean)
      }
    });

    if (existingProduct) {
      return next(new AppError('Product with this code, SKU, or barcode already exists', 400));
    }
  }

  const allowedUpdates = [
    'product_name', 'product_code', 'sku', 'barcode', 'description', 'product_type',
    'category_id', 'brand_id', 'unit_id', 'unit', 'purchase_price', 'selling_price',
    'mrp', 'tax_rate', 'hsn_code', 'weight', 'dimensions', 'min_stock_level',
    'max_stock_level', 'reorder_level', 'track_inventory', 'allow_negative_stock',
    'warranty_period', 'warranty_type', 'images', 'tags', 'custom_fields', 'status'
  ];

  const updates = {};
  allowedUpdates.forEach(field => {
    if (req.body[field] !== undefined) {
      updates[field] = req.body[field];
    }
  });

  updates.updated_by = req.user.id;

  await product.update(updates);

  const updatedProduct = await Product.findByPk(product.id, {
    include: [
      {
        model: Brand,
        as: 'brand',
        attributes: ['id', 'brand_name']
      },
      {
        model: Category,
        as: 'category',
        attributes: ['id', 'category_name']
      },
      {
        model: Unit,
        as: 'unitDetails',
        attributes: ['id', 'unit_name', 'unit_symbol']
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      product: updatedProduct
    }
  });
});

// Delete product
const deleteProduct = catchAsync(async (req, res, next) => {
  const product = await Product.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!product) {
    return next(new AppError('Product not found', 404));
  }

  await product.destroy();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

module.exports = {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct
};
