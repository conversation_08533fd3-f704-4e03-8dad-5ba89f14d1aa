const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const InvoiceTemplate = sequelize.define('InvoiceTemplate', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  template_name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  template_type: {
    type: DataTypes.ENUM('invoice', 'estimate', 'proforma', 'receipt', 'credit_note'),
    defaultValue: 'invoice'
  },
  template_code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  html_content: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  css_content: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  header_content: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  footer_content: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  variables: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  paper_size: {
    type: DataTypes.ENUM('A4', 'A5', 'Letter', 'Legal'),
    defaultValue: 'A4'
  },
  orientation: {
    type: DataTypes.ENUM('portrait', 'landscape'),
    defaultValue: 'portrait'
  },
  margins: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {
      top: '20mm',
      right: '20mm',
      bottom: '20mm',
      left: '20mm'
    }
  },
  show_header: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  show_footer: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  show_page_numbers: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  watermark_text: {
    type: DataTypes.STRING,
    allowNull: true
  },
  watermark_image: {
    type: DataTypes.STRING,
    allowNull: true
  },
  background_image: {
    type: DataTypes.STRING,
    allowNull: true
  },
  font_family: {
    type: DataTypes.STRING(100),
    defaultValue: 'Arial, sans-serif'
  },
  font_size: {
    type: DataTypes.INTEGER,
    defaultValue: 12
  },
  color_scheme: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {
      primary: '#007bff',
      secondary: '#6c757d',
      text: '#333333'
    }
  },
  is_default: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_system: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  version: {
    type: DataTypes.STRING(20),
    defaultValue: '1.0.0'
  },
  preview_image: {
    type: DataTypes.STRING,
    allowNull: true
  },
  usage_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'invoice_templates',
  paranoid: true,
  indexes: [
    {
      unique: true,
      fields: ['template_code']
    },
    {
      fields: ['company_id']
    },
    {
      fields: ['template_type']
    },
    {
      fields: ['is_default']
    },
    {
      fields: ['is_system']
    },
    {
      fields: ['is_active']
    }
  ]
});

module.exports = InvoiceTemplate;
