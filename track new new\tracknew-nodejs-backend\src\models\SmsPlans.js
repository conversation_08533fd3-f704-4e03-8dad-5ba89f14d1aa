const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const SmsPlans = sequelize.define('SmsPlans', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  plan_name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  plan_code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  sms_count: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'INR'
  },
  validity_days: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 30
  },
  plan_type: {
    type: DataTypes.ENUM('prepaid', 'postpaid', 'unlimited'),
    defaultValue: 'prepaid'
  },
  features: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  supported_countries: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: ['IN']
  },
  gateway_provider: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  gateway_config: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  per_sms_rate: {
    type: DataTypes.DECIMAL(8, 4),
    allowNull: true
  },
  overage_rate: {
    type: DataTypes.DECIMAL(8, 4),
    allowNull: true
  },
  discount_percentage: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    defaultValue: 0
  },
  is_popular: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  is_visible: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  terms_conditions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'sms_plans',
  paranoid: true,
  indexes: [
    {
      unique: true,
      fields: ['plan_code']
    },
    {
      fields: ['plan_type']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['is_visible']
    },
    {
      fields: ['is_popular']
    },
    {
      fields: ['sort_order']
    }
  ]
});

module.exports = SmsPlans;
