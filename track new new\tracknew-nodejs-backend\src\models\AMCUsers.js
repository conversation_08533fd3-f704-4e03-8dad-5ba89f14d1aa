const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const AMCUsers = sequelize.define('AMCUsers', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  amc_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'amc',
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  role: {
    type: DataTypes.ENUM('primary', 'secondary', 'supervisor'),
    defaultValue: 'primary'
  },
  assigned_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  assigned_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'amc_users',
  indexes: [
    {
      unique: true,
      fields: ['amc_id', 'user_id']
    },
    {
      fields: ['amc_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['is_active']
    }
  ]
});

module.exports = AMCUsers;
