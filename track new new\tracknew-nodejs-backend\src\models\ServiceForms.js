const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const ServiceForms = sequelize.define('ServiceForms', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  form_code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  form_type: {
    type: DataTypes.ENUM('service_request', 'feedback', 'inspection', 'maintenance', 'custom'),
    defaultValue: 'service_request'
  },
  form_structure: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: {
      fields: [],
      sections: [],
      validation: {},
      styling: {}
    }
  },
  form_fields: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: []
  },
  form_validation: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  form_styling: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  submission_settings: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {
      email_notifications: true,
      sms_notifications: false,
      auto_assign: false,
      create_service: false
    }
  },
  notification_settings: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {
      email_template: '',
      sms_template: '',
      recipients: []
    }
  },
  auto_assign_to: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  service_category_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'service_categories',
      key: 'id'
    }
  },
  is_public: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  requires_login: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  allow_file_upload: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  max_file_size: {
    type: DataTypes.INTEGER,
    defaultValue: 5120,
    comment: 'Max file size in KB'
  },
  allowed_file_types: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']
  },
  success_message: {
    type: DataTypes.TEXT,
    allowNull: true,
    defaultValue: 'Thank you for your submission. We will get back to you soon.'
  },
  redirect_url: {
    type: DataTypes.STRING,
    allowNull: true
  },
  form_header: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  form_footer: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  custom_css: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  custom_js: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  submission_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  last_submission_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  version: {
    type: DataTypes.STRING(20),
    defaultValue: '1.0.0'
  },
  status: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    comment: '0=inactive, 1=active, 2=archived'
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'service_forms',
  paranoid: true,
  indexes: [
    {
      unique: true,
      fields: ['form_code']
    },
    {
      fields: ['company_id']
    },
    {
      fields: ['form_type']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['is_public']
    },
    {
      fields: ['service_category_id']
    },
    {
      fields: ['auto_assign_to']
    }
  ]
});

module.exports = ServiceForms;
