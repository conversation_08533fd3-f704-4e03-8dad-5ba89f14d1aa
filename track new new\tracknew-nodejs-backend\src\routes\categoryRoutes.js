const express = require('express');
const { body } = require('express-validator');

const categoryController = require('../controllers/categoryController');
const { protect, restrictTo, restrictToCompany } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Protect all routes
router.use(protect);
router.use(restrictToCompany);

// Validation rules
const createCategoryValidation = [
  body('category_name')
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('Category name is required and must be between 2-255 characters'),
  body('category_code')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Category code must be less than 50 characters'),
  body('parent_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Parent ID must be a valid number'),
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a positive number'),
  body('status')
    .optional()
    .isBoolean()
    .withMessage('Status must be a boolean value')
];

const updateCategoryValidation = [
  body('category_name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('Category name must be between 2-255 characters'),
  body('category_code')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Category code must be less than 50 characters'),
  body('parent_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Parent ID must be a valid number'),
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a positive number'),
  body('status')
    .optional()
    .isBoolean()
    .withMessage('Status must be a boolean value')
];

// Routes
router.get('/tree', categoryController.getCategoryTree);

router
  .route('/')
  .get(categoryController.getCategories)
  .post(createCategoryValidation, validateRequest, categoryController.createCategory);

router
  .route('/:id')
  .get(categoryController.getCategory)
  .put(updateCategoryValidation, validateRequest, categoryController.updateCategory)
  .delete(restrictTo('admin', 'sub_admin'), categoryController.deleteCategory);

module.exports = router;
