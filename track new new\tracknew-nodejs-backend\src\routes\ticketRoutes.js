const express = require('express');
const { body } = require('express-validator');

const ticketController = require('../controllers/ticketController');
const { protect, restrictTo, restrictToCompany } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Protect all routes
router.use(protect);
router.use(restrictToCompany);

// Validation rules
const createTicketValidation = [
  body('subject')
    .trim()
    .isLength({ min: 5, max: 255 })
    .withMessage('Subject is required and must be between 5-255 characters'),
  body('description')
    .trim()
    .isLength({ min: 10 })
    .withMessage('Description is required and must be at least 10 characters'),
  body('ticket_type')
    .optional()
    .isIn(['support', 'bug_report', 'feature_request', 'complaint', 'inquiry', 'technical', 'billing'])
    .withMessage('Invalid ticket type'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent', 'critical'])
    .withMessage('Invalid priority level'),
  body('customer_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Customer ID must be a valid number'),
  body('assigned_to')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Assigned to must be a valid user ID'),
  body('source')
    .optional()
    .isIn(['email', 'phone', 'web', 'chat', 'social_media', 'walk_in'])
    .withMessage('Invalid source'),
  body('customer_email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('customer_phone')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Phone number must be less than 20 characters'),
  body('due_date')
    .optional()
    .isISO8601()
    .withMessage('Due date must be a valid date'),
  body('related_service_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Related service ID must be a valid number'),
  body('related_product_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Related product ID must be a valid number'),
  body('related_order_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Related order ID must be a valid number')
];

const updateTicketValidation = [
  body('subject')
    .optional()
    .trim()
    .isLength({ min: 5, max: 255 })
    .withMessage('Subject must be between 5-255 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ min: 10 })
    .withMessage('Description must be at least 10 characters'),
  body('ticket_type')
    .optional()
    .isIn(['support', 'bug_report', 'feature_request', 'complaint', 'inquiry', 'technical', 'billing'])
    .withMessage('Invalid ticket type'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent', 'critical'])
    .withMessage('Invalid priority level'),
  body('status')
    .optional()
    .isIn(['open', 'in_progress', 'pending', 'resolved', 'closed', 'cancelled'])
    .withMessage('Invalid status'),
  body('assigned_to')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Assigned to must be a valid user ID'),
  body('escalated_to')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Escalated to must be a valid user ID'),
  body('escalation_level')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Escalation level must be a positive number'),
  body('satisfaction_rating')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Satisfaction rating must be between 1 and 5'),
  body('due_date')
    .optional()
    .isISO8601()
    .withMessage('Due date must be a valid date'),
  body('resolution_date')
    .optional()
    .isISO8601()
    .withMessage('Resolution date must be a valid date'),
  body('follow_up_date')
    .optional()
    .isISO8601()
    .withMessage('Follow up date must be a valid date'),
  body('follow_up_required')
    .optional()
    .isBoolean()
    .withMessage('Follow up required must be a boolean value'),
  body('is_billable')
    .optional()
    .isBoolean()
    .withMessage('Is billable must be a boolean value'),
  body('billable_hours')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Billable hours must be a positive number'),
  body('hourly_rate')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Hourly rate must be a positive number'),
  body('sla_breach')
    .optional()
    .isBoolean()
    .withMessage('SLA breach must be a boolean value')
];

// Routes
router.get('/stats', ticketController.getTicketStats);

router
  .route('/')
  .get(ticketController.getTickets)
  .post(createTicketValidation, validateRequest, ticketController.createTicket);

router
  .route('/:id')
  .get(ticketController.getTicket)
  .put(updateTicketValidation, validateRequest, ticketController.updateTicket)
  .delete(restrictTo('admin', 'sub_admin'), ticketController.deleteTicket);

module.exports = router;
