const { PurchaseOrder, PurchaseOrderItem, PurchaseOrderPayment, Supplier, Company, User, Product, Warehouse } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all purchase orders with filtering and pagination
const getPurchaseOrders = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    status,
    payment_status,
    supplier_id,
    search,
    sort_by = 'created_at',
    sort_order = 'DESC',
    date_from,
    date_to
  } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = { company_id: req.user.company_id };

  // Apply filters
  if (status) {
    whereClause.status = status;
  }

  if (payment_status) {
    whereClause.payment_status = payment_status;
  }

  if (supplier_id) {
    whereClause.supplier_id = supplier_id;
  }

  if (date_from && date_to) {
    whereClause.po_date = {
      [Op.between]: [new Date(date_from), new Date(date_to)]
    };
  }

  if (search) {
    whereClause[Op.or] = [
      { po_number: { [Op.like]: `%${search}%` } },
      { reference_number: { [Op.like]: `%${search}%` } },
      { quotation_reference: { [Op.like]: `%${search}%` } },
      { notes: { [Op.like]: `%${search}%` } }
    ];
  }

  const { count, rows: purchaseOrders } = await PurchaseOrder.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: Supplier,
        as: 'supplier',
        attributes: ['id', 'supplier_name', 'supplier_code', 'email', 'mobile_number']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: Warehouse,
        as: 'warehouse',
        attributes: ['id', 'warehouse_name', 'warehouse_code']
      },
      {
        model: PurchaseOrderItem,
        as: 'items',
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'product_name', 'product_code', 'unit']
          }
        ]
      }
    ],
    order: [[sort_by, sort_order.toUpperCase()]],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.status(200).json({
    status: 'success',
    data: {
      purchase_orders: purchaseOrders,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: count,
        total_pages: Math.ceil(count / limit)
      }
    }
  });
});

// Get purchase order by ID
const getPurchaseOrder = catchAsync(async (req, res, next) => {
  const purchaseOrder = await PurchaseOrder.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    },
    include: [
      {
        model: Supplier,
        as: 'supplier'
      },
      {
        model: Company,
        as: 'company'
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'approver',
        attributes: ['id', 'name', 'email']
      },
      {
        model: Warehouse,
        as: 'warehouse'
      },
      {
        model: PurchaseOrderItem,
        as: 'items',
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'product_name', 'product_code', 'unit', 'hsn_code']
          }
        ],
        order: [['sort_order', 'ASC']]
      },
      {
        model: PurchaseOrderPayment,
        as: 'payments',
        order: [['payment_date', 'DESC']]
      }
    ]
  });

  if (!purchaseOrder) {
    return next(new AppError('Purchase order not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      purchase_order: purchaseOrder
    }
  });
});

// Create new purchase order
const createPurchaseOrder = catchAsync(async (req, res, next) => {
  const {
    supplier_id,
    po_date = new Date(),
    expected_delivery_date,
    warehouse_id,
    items = [],
    discount_type,
    discount_value = 0,
    shipping_amount = 0,
    other_charges = 0,
    terms_conditions,
    notes,
    internal_notes,
    delivery_address,
    billing_address,
    payment_terms,
    delivery_terms,
    reference_number,
    quotation_reference,
    priority = 'medium'
  } = req.body;

  // Validate supplier exists
  const supplier = await Supplier.findOne({
    where: { id: supplier_id, company_id: req.user.company_id }
  });

  if (!supplier) {
    return next(new AppError('Supplier not found', 404));
  }

  // Validate warehouse if provided
  if (warehouse_id) {
    const warehouse = await Warehouse.findOne({
      where: { id: warehouse_id, company_id: req.user.company_id }
    });

    if (!warehouse) {
      return next(new AppError('Warehouse not found', 404));
    }
  }

  // Calculate totals
  let subtotal = 0;
  const processedItems = items.map((item, index) => {
    const lineTotal = item.quantity_ordered * item.unit_price;
    const itemDiscountAmount = item.discount_type === 'percentage' 
      ? (lineTotal * item.discount_value) / 100 
      : (item.discount_value || 0);
    const taxableAmount = lineTotal - itemDiscountAmount;
    
    const cgstAmount = (taxableAmount * (item.cgst_rate || 0)) / 100;
    const sgstAmount = (taxableAmount * (item.sgst_rate || 0)) / 100;
    const igstAmount = (taxableAmount * (item.igst_rate || 0)) / 100;
    
    const taxAmount = cgstAmount + sgstAmount + igstAmount;
    const totalAmount = taxableAmount + taxAmount;
    
    subtotal += totalAmount;
    
    return {
      ...item,
      quantity_pending: item.quantity_ordered,
      discount_amount: itemDiscountAmount,
      taxable_amount: taxableAmount,
      cgst_amount: cgstAmount,
      sgst_amount: sgstAmount,
      igst_amount: igstAmount,
      tax_amount: taxAmount,
      total_amount: totalAmount,
      sort_order: index + 1
    };
  });

  // Calculate PO level discount
  const discountAmount = discount_type === 'percentage' 
    ? (subtotal * discount_value) / 100 
    : (discount_value || 0);

  const taxAmount = processedItems.reduce((sum, item) => sum + item.tax_amount, 0);
  const totalAmount = subtotal - discountAmount + parseFloat(shipping_amount || 0) + parseFloat(other_charges || 0);
  const balanceAmount = totalAmount; // Initially, balance equals total

  // Create purchase order
  const purchaseOrder = await PurchaseOrder.create({
    company_id: req.user.company_id,
    supplier_id,
    po_date,
    expected_delivery_date,
    warehouse_id,
    subtotal,
    discount_type,
    discount_value,
    discount_amount,
    tax_amount: taxAmount,
    shipping_amount,
    other_charges,
    total_amount,
    paid_amount: 0,
    balance_amount: balanceAmount,
    terms_conditions,
    notes,
    internal_notes,
    delivery_address,
    billing_address,
    payment_terms,
    delivery_terms,
    reference_number,
    quotation_reference,
    priority,
    created_by: req.user.id
  });

  // Create purchase order items
  if (processedItems.length > 0) {
    const itemsToCreate = processedItems.map(item => ({
      ...item,
      purchase_order_id: purchaseOrder.id
    }));
    
    await PurchaseOrderItem.bulkCreate(itemsToCreate);
  }

  // Fetch created purchase order with associations
  const createdPurchaseOrder = await PurchaseOrder.findByPk(purchaseOrder.id, {
    include: [
      {
        model: Supplier,
        as: 'supplier'
      },
      {
        model: PurchaseOrderItem,
        as: 'items',
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'product_name', 'product_code', 'unit']
          }
        ]
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      purchase_order: createdPurchaseOrder
    }
  });
});

// Update purchase order
const updatePurchaseOrder = catchAsync(async (req, res, next) => {
  const purchaseOrder = await PurchaseOrder.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!purchaseOrder) {
    return next(new AppError('Purchase order not found', 404));
  }

  // Check if PO can be updated
  if (purchaseOrder.status === 'received') {
    return next(new AppError('Cannot update received purchase order', 400));
  }

  const allowedUpdates = [
    'expected_delivery_date', 'actual_delivery_date', 'status', 'payment_status',
    'terms_conditions', 'notes', 'internal_notes', 'delivery_address',
    'billing_address', 'payment_terms', 'delivery_terms', 'reference_number',
    'quotation_reference', 'priority', 'other_charges'
  ];

  const updates = {};
  allowedUpdates.forEach(field => {
    if (req.body[field] !== undefined) {
      updates[field] = req.body[field];
    }
  });

  // Recalculate total if other charges changed
  if (updates.other_charges !== undefined) {
    updates.total_amount = purchaseOrder.subtotal - purchaseOrder.discount_amount + 
                          purchaseOrder.shipping_amount + parseFloat(updates.other_charges || 0);
    updates.balance_amount = updates.total_amount - purchaseOrder.paid_amount;
  }

  updates.updated_by = req.user.id;

  await purchaseOrder.update(updates);

  const updatedPurchaseOrder = await PurchaseOrder.findByPk(purchaseOrder.id, {
    include: [
      {
        model: Supplier,
        as: 'supplier'
      },
      {
        model: PurchaseOrderItem,
        as: 'items'
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      purchase_order: updatedPurchaseOrder
    }
  });
});

// Delete purchase order
const deletePurchaseOrder = catchAsync(async (req, res, next) => {
  const purchaseOrder = await PurchaseOrder.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!purchaseOrder) {
    return next(new AppError('Purchase order not found', 404));
  }

  if (purchaseOrder.status === 'received' || purchaseOrder.paid_amount > 0) {
    return next(new AppError('Cannot delete received purchase order or PO with payments', 400));
  }

  await purchaseOrder.destroy();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

module.exports = {
  getPurchaseOrders,
  getPurchaseOrder,
  createPurchaseOrder,
  updatePurchaseOrder,
  deletePurchaseOrder
};
