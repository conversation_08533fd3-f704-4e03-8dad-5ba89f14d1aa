const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Tickets = sequelize.define('Tickets', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  ticket_number: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  customer_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'customers',
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  subject: {
    type: DataTypes.STRING,
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  ticket_type: {
    type: DataTypes.ENUM('support', 'bug_report', 'feature_request', 'complaint', 'inquiry', 'technical', 'billing'),
    defaultValue: 'support'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent', 'critical'),
    defaultValue: 'medium'
  },
  status: {
    type: DataTypes.ENUM('open', 'in_progress', 'pending', 'resolved', 'closed', 'cancelled'),
    defaultValue: 'open'
  },
  category: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  subcategory: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  assigned_to: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  assigned_team: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  source: {
    type: DataTypes.ENUM('email', 'phone', 'web', 'chat', 'social_media', 'walk_in'),
    defaultValue: 'web'
  },
  customer_name: {
    type: DataTypes.STRING,
    allowNull: true
  },
  customer_email: {
    type: DataTypes.STRING,
    allowNull: true
  },
  customer_phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  due_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  resolution_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  first_response_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  resolution_time: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Resolution time in minutes'
  },
  satisfaction_rating: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 5
    }
  },
  satisfaction_feedback: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  related_service_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'services',
      key: 'id'
    }
  },
  related_product_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  related_order_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'sales',
      key: 'id'
    }
  },
  escalation_level: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  escalated_to: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  escalated_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  sla_breach: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  internal_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  resolution_summary: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  follow_up_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  follow_up_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  is_billable: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  billable_hours: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  hourly_rate: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  total_cost: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'tickets',
  paranoid: true,
  indexes: [
    {
      unique: true,
      fields: ['ticket_number']
    },
    {
      fields: ['company_id']
    },
    {
      fields: ['customer_id']
    },
    {
      fields: ['assigned_to']
    },
    {
      fields: ['status']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['ticket_type']
    },
    {
      fields: ['source']
    },
    {
      fields: ['due_date']
    },
    {
      fields: ['customer_email']
    }
  ],
  hooks: {
    beforeCreate: async (ticket) => {
      if (!ticket.ticket_number) {
        const count = await Tickets.count({ where: { company_id: ticket.company_id } });
        ticket.ticket_number = `TKT-${ticket.company_id}-${String(count + 1).padStart(6, '0')}`;
      }
    }
  }
});

module.exports = Tickets;
