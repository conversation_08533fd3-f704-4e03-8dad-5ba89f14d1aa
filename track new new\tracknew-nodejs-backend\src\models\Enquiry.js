const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Enquiry = sequelize.define('Enquiry', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  enquiry_number: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  customer_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'customers',
      key: 'id'
    }
  },
  enquiry_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  source: {
    type: DataTypes.ENUM('website', 'phone', 'email', 'walk_in', 'referral', 'social_media', 'advertisement', 'other'),
    defaultValue: 'website'
  },
  subject: {
    type: DataTypes.STRING,
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  customer_name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  customer_email: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isEmail: true
    }
  },
  customer_phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  customer_company: {
    type: DataTypes.STRING,
    allowNull: true
  },
  customer_address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  product_interest: {
    type: DataTypes.STRING,
    allowNull: true
  },
  service_interest: {
    type: DataTypes.STRING,
    allowNull: true
  },
  budget_range: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  timeline: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium'
  },
  status: {
    type: DataTypes.ENUM('new', 'contacted', 'qualified', 'quoted', 'negotiating', 'won', 'lost', 'on_hold'),
    defaultValue: 'new'
  },
  assigned_to: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  follow_up_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  expected_closure_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  estimated_value: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true
  },
  actual_value: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true
  },
  conversion_probability: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 0,
      max: 100
    }
  },
  lost_reason: {
    type: DataTypes.STRING,
    allowNull: true
  },
  competitor: {
    type: DataTypes.STRING,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  converted_to_lead: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  lead_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'leads',
      key: 'id'
    }
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'enquiries',
  paranoid: true,
  indexes: [
    {
      fields: ['enquiry_number']
    },
    {
      fields: ['company_id']
    },
    {
      fields: ['customer_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['source']
    },
    {
      fields: ['assigned_to']
    },
    {
      fields: ['enquiry_date']
    },
    {
      fields: ['follow_up_date']
    },
    {
      fields: ['customer_email']
    },
    {
      fields: ['customer_phone']
    }
  ],
  hooks: {
    beforeCreate: async (enquiry) => {
      if (!enquiry.enquiry_number) {
        const count = await Enquiry.count({ where: { company_id: enquiry.company_id } });
        enquiry.enquiry_number = `ENQ-${enquiry.company_id}-${String(count + 1).padStart(6, '0')}`;
      }
    }
  }
});

module.exports = Enquiry;
