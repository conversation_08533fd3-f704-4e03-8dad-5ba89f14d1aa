const express = require('express');
const { body } = require('express-validator');

const supplierController = require('../controllers/supplierController');
const { protect, restrictTo, restrictToCompany } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Protect all routes
router.use(protect);
router.use(restrictToCompany);

// Validation rules
const createSupplierValidation = [
  body('supplier_name')
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('Supplier name is required and must be between 2-255 characters'),
  body('supplier_type')
    .optional()
    .isIn(['individual', 'business'])
    .withMessage('Supplier type must be individual or business'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('mobile_number')
    .optional()
    .isMobilePhone('any')
    .withMessage('Please provide a valid mobile number'),
  body('phone_number')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Phone number must be less than 20 characters'),
  body('website')
    .optional()
    .isURL()
    .withMessage('Please provide a valid website URL'),
  body('gst_number')
    .optional()
    .isLength({ min: 15, max: 15 })
    .withMessage('GST number must be exactly 15 characters'),
  body('pan_number')
    .optional()
    .isLength({ min: 10, max: 10 })
    .withMessage('PAN number must be exactly 10 characters'),
  body('payment_terms')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Payment terms must be a positive number'),
  body('credit_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Credit limit must be a positive number'),
  body('opening_balance')
    .optional()
    .isFloat()
    .withMessage('Opening balance must be a valid number'),
  body('supplier_category')
    .optional()
    .isIn(['regular', 'preferred', 'critical'])
    .withMessage('Supplier category must be regular, preferred, or critical'),
  body('rating')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('billing_country')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Billing country must be less than 100 characters'),
  body('shipping_country')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Shipping country must be less than 100 characters')
];

const updateSupplierValidation = [
  body('supplier_name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('Supplier name must be between 2-255 characters'),
  body('supplier_type')
    .optional()
    .isIn(['individual', 'business'])
    .withMessage('Supplier type must be individual or business'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('mobile_number')
    .optional()
    .isMobilePhone('any')
    .withMessage('Please provide a valid mobile number'),
  body('phone_number')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Phone number must be less than 20 characters'),
  body('website')
    .optional()
    .isURL()
    .withMessage('Please provide a valid website URL'),
  body('gst_number')
    .optional()
    .isLength({ min: 15, max: 15 })
    .withMessage('GST number must be exactly 15 characters'),
  body('pan_number')
    .optional()
    .isLength({ min: 10, max: 10 })
    .withMessage('PAN number must be exactly 10 characters'),
  body('payment_terms')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Payment terms must be a positive number'),
  body('credit_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Credit limit must be a positive number'),
  body('supplier_category')
    .optional()
    .isIn(['regular', 'preferred', 'critical'])
    .withMessage('Supplier category must be regular, preferred, or critical'),
  body('rating')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('status')
    .optional()
    .isBoolean()
    .withMessage('Status must be a boolean value'),
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Notes must be less than 2000 characters')
];

// Routes
router.get('/stats', supplierController.getSupplierStats);

router
  .route('/')
  .get(supplierController.getSuppliers)
  .post(createSupplierValidation, validateRequest, supplierController.createSupplier);

router
  .route('/:id')
  .get(supplierController.getSupplier)
  .put(updateSupplierValidation, validateRequest, supplierController.updateSupplier)
  .delete(restrictTo('admin', 'sub_admin'), supplierController.deleteSupplier);

module.exports = router;
