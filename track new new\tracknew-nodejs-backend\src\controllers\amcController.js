const { AMC, AMCProduct, AMCDates, AMCUsers, Customer, Company, User, Product, Invoice } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all AMCs with filtering and pagination
const getAMCs = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    status,
    customer_id,
    search,
    sort_by = 'created_at',
    sort_order = 'DESC',
    date_from,
    date_to
  } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = { company_id: req.user.company_id };

  // Apply filters
  if (status) {
    whereClause.status = status;
  }

  if (customer_id) {
    whereClause.customer_id = customer_id;
  }

  if (date_from && date_to) {
    whereClause.start_date = {
      [Op.between]: [new Date(date_from), new Date(date_to)]
    };
  }

  if (search) {
    whereClause[Op.or] = [
      { amc_number: { [Op.like]: `%${search}%` } },
      { reference_number: { [Op.like]: `%${search}%` } },
      { notes: { [Op.like]: `%${search}%` } }
    ];
  }

  const { count, rows: amcs } = await AMC.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'customer_code', 'email', 'mobile_number']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: AMCProduct,
        as: 'products',
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'product_name', 'product_code']
          }
        ]
      },
      {
        model: AMCDates,
        as: 'dates',
        attributes: ['id', 'service_date', 'status', 'assigned_technician'],
        limit: 5,
        order: [['service_date', 'ASC']]
      }
    ],
    order: [[sort_by, sort_order.toUpperCase()]],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.status(200).json({
    status: 'success',
    data: {
      amcs,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: count,
        total_pages: Math.ceil(count / limit)
      }
    }
  });
});

// Get AMC by ID
const getAMC = catchAsync(async (req, res, next) => {
  const amc = await AMC.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    },
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: Company,
        as: 'company'
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: AMCProduct,
        as: 'products',
        include: [
          {
            model: Product,
            as: 'product'
          }
        ]
      },
      {
        model: AMCDates,
        as: 'dates',
        include: [
          {
            model: User,
            as: 'technician',
            attributes: ['id', 'name', 'email']
          },
          {
            model: Invoice,
            as: 'invoice',
            attributes: ['id', 'invoice_number', 'status']
          }
        ],
        order: [['service_date', 'ASC']]
      },
      {
        model: User,
        as: 'assignedUsers',
        through: { attributes: ['assigned_date', 'role'] },
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  if (!amc) {
    return next(new AppError('AMC not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      amc
    }
  });
});

// Create new AMC
const createAMC = catchAsync(async (req, res, next) => {
  const {
    customer_id,
    amc_type = 'annual',
    start_date,
    end_date,
    service_frequency = 'monthly',
    total_services,
    products = [],
    assigned_users = [],
    service_dates = [],
    amount = 0,
    payment_terms,
    terms_conditions,
    notes,
    reference_number
  } = req.body;

  // Validate customer exists
  const customer = await Customer.findOne({
    where: { id: customer_id, company_id: req.user.company_id }
  });

  if (!customer) {
    return next(new AppError('Customer not found', 404));
  }

  // Create AMC
  const amc = await AMC.create({
    company_id: req.user.company_id,
    customer_id,
    amc_type,
    start_date,
    end_date,
    service_frequency,
    total_services,
    amount,
    payment_terms,
    terms_conditions,
    notes,
    reference_number,
    created_by: req.user.id
  });

  // Create AMC products
  if (products.length > 0) {
    const productsToCreate = products.map(product => ({
      amc_id: amc.id,
      product_id: product.product_id,
      quantity: product.quantity || 1,
      unit_price: product.unit_price || 0,
      total_amount: (product.quantity || 1) * (product.unit_price || 0),
      warranty_period: product.warranty_period,
      service_instructions: product.service_instructions
    }));
    
    await AMCProduct.bulkCreate(productsToCreate);
  }

  // Create AMC service dates
  if (service_dates.length > 0) {
    const datesToCreate = service_dates.map((date, index) => ({
      amc_id: amc.id,
      service_date: date.service_date,
      assigned_technician: date.assigned_technician,
      service_type: date.service_type || 'maintenance',
      priority: date.priority || 'normal',
      estimated_duration: date.estimated_duration,
      notes: date.notes,
      sort_order: index + 1
    }));
    
    await AMCDates.bulkCreate(datesToCreate);
  }

  // Assign users to AMC
  if (assigned_users.length > 0) {
    const usersToAssign = assigned_users.map(user => ({
      amc_id: amc.id,
      user_id: user.user_id,
      role: user.role || 'technician',
      assigned_date: new Date(),
      assigned_by: req.user.id
    }));
    
    await AMCUsers.bulkCreate(usersToAssign);
  }

  // Fetch created AMC with associations
  const createdAMC = await AMC.findByPk(amc.id, {
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: AMCProduct,
        as: 'products',
        include: [
          {
            model: Product,
            as: 'product'
          }
        ]
      },
      {
        model: AMCDates,
        as: 'dates'
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      amc: createdAMC
    }
  });
});

// Update AMC
const updateAMC = catchAsync(async (req, res, next) => {
  const amc = await AMC.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!amc) {
    return next(new AppError('AMC not found', 404));
  }

  const allowedUpdates = [
    'end_date', 'status', 'service_frequency', 'total_services', 'amount',
    'payment_terms', 'terms_conditions', 'notes', 'reference_number'
  ];

  const updates = {};
  allowedUpdates.forEach(field => {
    if (req.body[field] !== undefined) {
      updates[field] = req.body[field];
    }
  });

  updates.updated_by = req.user.id;

  await amc.update(updates);

  const updatedAMC = await AMC.findByPk(amc.id, {
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: AMCProduct,
        as: 'products'
      },
      {
        model: AMCDates,
        as: 'dates'
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      amc: updatedAMC
    }
  });
});

// Delete AMC
const deleteAMC = catchAsync(async (req, res, next) => {
  const amc = await AMC.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!amc) {
    return next(new AppError('AMC not found', 404));
  }

  if (amc.status === 'active') {
    return next(new AppError('Cannot delete active AMC', 400));
  }

  await amc.destroy();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

module.exports = {
  getAMCs,
  getAMC,
  createAMC,
  updateAMC,
  deleteAMC
};
