const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const InvoiceItem = sequelize.define('InvoiceItem', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  invoice_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'invoices',
      key: 'id'
    }
  },
  product_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  item_type: {
    type: DataTypes.ENUM('product', 'service', 'custom'),
    defaultValue: 'product'
  },
  item_name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  item_description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  item_code: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  hsn_code: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  quantity: {
    type: DataTypes.DECIMAL(10, 3),
    allowNull: false,
    defaultValue: 1
  },
  unit: {
    type: DataTypes.STRING(20),
    allowNull: true,
    defaultValue: 'pcs'
  },
  unit_price: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  discount_type: {
    type: DataTypes.ENUM('percentage', 'fixed'),
    allowNull: true
  },
  discount_value: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0
  },
  discount_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  taxable_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  tax_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0
  },
  tax_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  cgst_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0
  },
  cgst_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  sgst_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0
  },
  sgst_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  igst_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0
  },
  igst_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  cess_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0
  },
  cess_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  total_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  sort_order: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  serial_numbers: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  batch_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  expiry_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  warehouse_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'warehouses',
      key: 'id'
    }
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'invoice_items',
  indexes: [
    {
      fields: ['invoice_id']
    },
    {
      fields: ['product_id']
    },
    {
      fields: ['item_type']
    },
    {
      fields: ['sort_order']
    },
    {
      fields: ['warehouse_id']
    }
  ],
  hooks: {
    beforeSave: (item) => {
      // Calculate amounts
      const lineTotal = item.quantity * item.unit_price;
      
      // Calculate discount
      if (item.discount_type === 'percentage') {
        item.discount_amount = (lineTotal * item.discount_value) / 100;
      } else {
        item.discount_amount = item.discount_value || 0;
      }
      
      // Calculate taxable amount
      item.taxable_amount = lineTotal - item.discount_amount;
      
      // Calculate tax amounts
      item.cgst_amount = (item.taxable_amount * item.cgst_rate) / 100;
      item.sgst_amount = (item.taxable_amount * item.sgst_rate) / 100;
      item.igst_amount = (item.taxable_amount * item.igst_rate) / 100;
      item.cess_amount = (item.taxable_amount * item.cess_rate) / 100;
      
      item.tax_amount = item.cgst_amount + item.sgst_amount + item.igst_amount + item.cess_amount;
      item.total_amount = item.taxable_amount + item.tax_amount;
    }
  }
});

module.exports = InvoiceItem;
