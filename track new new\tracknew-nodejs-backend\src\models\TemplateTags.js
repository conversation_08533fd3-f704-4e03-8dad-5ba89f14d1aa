const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const TemplateTags = sequelize.define('TemplateTags', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  slug: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  color: {
    type: DataTypes.STRING(7),
    allowNull: true,
    defaultValue: '#007bff'
  },
  icon: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  status: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    comment: '0=inactive, 1=active'
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  meta_title: {
    type: DataTypes.STRING,
    allowNull: true
  },
  meta_description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'template_tags',
  paranoid: true,
  indexes: [
    {
      unique: true,
      fields: ['name']
    },
    {
      unique: true,
      fields: ['slug']
    },
    {
      fields: ['status']
    },
    {
      fields: ['sort_order']
    }
  ],
  hooks: {
    beforeCreate: (tag) => {
      if (!tag.slug) {
        tag.slug = tag.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');
      }
    },
    beforeUpdate: (tag) => {
      if (tag.changed('name') && !tag.changed('slug')) {
        tag.slug = tag.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');
      }
    }
  }
});

module.exports = TemplateTags;
