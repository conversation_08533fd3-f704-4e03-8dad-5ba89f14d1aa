const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Supplier = sequelize.define('Supplier', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  supplier_code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  supplier_name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 255]
    }
  },
  supplier_type: {
    type: DataTypes.ENUM('individual', 'business'),
    defaultValue: 'business'
  },
  contact_person: {
    type: DataTypes.STRING,
    allowNull: true
  },
  email: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isEmail: true
    }
  },
  mobile_number: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  phone_number: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  fax_number: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  website: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isUrl: true
    }
  },
  gst_number: {
    type: DataTypes.STRING(15),
    allowNull: true,
    validate: {
      len: [15, 15]
    }
  },
  pan_number: {
    type: DataTypes.STRING(10),
    allowNull: true,
    validate: {
      len: [10, 10]
    }
  },
  tan_number: {
    type: DataTypes.STRING(10),
    allowNull: true
  },
  registration_number: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  billing_address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  billing_city: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  billing_state: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  billing_country: {
    type: DataTypes.STRING(100),
    allowNull: true,
    defaultValue: 'India'
  },
  billing_pincode: {
    type: DataTypes.STRING(10),
    allowNull: true
  },
  shipping_address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  shipping_city: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  shipping_state: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  shipping_country: {
    type: DataTypes.STRING(100),
    allowNull: true,
    defaultValue: 'India'
  },
  shipping_pincode: {
    type: DataTypes.STRING(10),
    allowNull: true
  },
  bank_name: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  bank_account_number: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  bank_ifsc_code: {
    type: DataTypes.STRING(11),
    allowNull: true
  },
  bank_branch: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  payment_terms: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 30,
    comment: 'Payment terms in days'
  },
  credit_limit: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0
  },
  opening_balance: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0
  },
  current_balance: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  supplier_category: {
    type: DataTypes.ENUM('regular', 'preferred', 'critical'),
    defaultValue: 'regular'
  },
  rating: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 5
    }
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'suppliers',
  paranoid: true,
  indexes: [
    {
      fields: ['supplier_code']
    },
    {
      fields: ['company_id']
    },
    {
      fields: ['supplier_name']
    },
    {
      fields: ['email']
    },
    {
      fields: ['mobile_number']
    },
    {
      fields: ['gst_number']
    },
    {
      fields: ['status']
    },
    {
      fields: ['supplier_category']
    }
  ],
  hooks: {
    beforeCreate: async (supplier) => {
      if (!supplier.supplier_code) {
        const count = await Supplier.count({ where: { company_id: supplier.company_id } });
        supplier.supplier_code = `SUP-${supplier.company_id}-${String(count + 1).padStart(6, '0')}`;
      }
    }
  }
});

module.exports = Supplier;
