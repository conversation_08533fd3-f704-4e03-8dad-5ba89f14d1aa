const { Sales, SalesItem, SalesPayment, Customer, Company, User, Product, Estimation, Proforma } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all sales with filtering and pagination
const getSales = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    status,
    payment_status,
    customer_id,
    search,
    sort_by = 'created_at',
    sort_order = 'DESC',
    date_from,
    date_to
  } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = { company_id: req.user.company_id };

  // Apply filters
  if (status) {
    whereClause.status = status;
  }

  if (payment_status) {
    whereClause.payment_status = payment_status;
  }

  if (customer_id) {
    whereClause.customer_id = customer_id;
  }

  if (date_from && date_to) {
    whereClause.sales_date = {
      [Op.between]: [new Date(date_from), new Date(date_to)]
    };
  }

  if (search) {
    whereClause[Op.or] = [
      { sales_number: { [Op.like]: `%${search}%` } },
      { reference_number: { [Op.like]: `%${search}%` } },
      { po_number: { [Op.like]: `%${search}%` } },
      { notes: { [Op.like]: `%${search}%` } }
    ];
  }

  const { count, rows: sales } = await Sales.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'customer_code', 'email', 'mobile_number']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: SalesItem,
        as: 'items',
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'product_name', 'product_code', 'unit']
          }
        ]
      }
    ],
    order: [[sort_by, sort_order.toUpperCase()]],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.status(200).json({
    status: 'success',
    data: {
      sales,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: count,
        total_pages: Math.ceil(count / limit)
      }
    }
  });
});

// Get sale by ID
const getSale = catchAsync(async (req, res, next) => {
  const sale = await Sales.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    },
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: Company,
        as: 'company'
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: Estimation,
        as: 'estimation',
        attributes: ['id', 'estimation_number', 'status']
      },
      {
        model: Proforma,
        as: 'proforma',
        attributes: ['id', 'proforma_number', 'status']
      },
      {
        model: SalesItem,
        as: 'items',
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'product_name', 'product_code', 'unit', 'hsn_code']
          }
        ],
        order: [['sort_order', 'ASC']]
      },
      {
        model: SalesPayment,
        as: 'payments',
        order: [['payment_date', 'DESC']]
      }
    ]
  });

  if (!sale) {
    return next(new AppError('Sale not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      sale
    }
  });
});

// Create new sale
const createSale = catchAsync(async (req, res, next) => {
  const {
    customer_id,
    estimation_id,
    proforma_id,
    sales_date = new Date(),
    delivery_date,
    items = [],
    discount_type,
    discount_value = 0,
    shipping_amount = 0,
    adjustment_amount = 0,
    terms_conditions,
    notes,
    internal_notes,
    billing_address,
    shipping_address,
    payment_terms,
    delivery_terms,
    reference_number,
    po_number
  } = req.body;

  // Validate customer exists
  const customer = await Customer.findOne({
    where: { id: customer_id, company_id: req.user.company_id }
  });

  if (!customer) {
    return next(new AppError('Customer not found', 404));
  }

  // Calculate totals
  let subtotal = 0;
  const processedItems = items.map((item, index) => {
    const lineTotal = item.quantity * item.unit_price;
    const itemDiscountAmount = item.discount_type === 'percentage'
      ? (lineTotal * item.discount_value) / 100
      : (item.discount_value || 0);
    const taxableAmount = lineTotal - itemDiscountAmount;

    const cgstAmount = (taxableAmount * (item.cgst_rate || 0)) / 100;
    const sgstAmount = (taxableAmount * (item.sgst_rate || 0)) / 100;
    const igstAmount = (taxableAmount * (item.igst_rate || 0)) / 100;
    const cessAmount = (taxableAmount * (item.cess_rate || 0)) / 100;

    const taxAmount = cgstAmount + sgstAmount + igstAmount + cessAmount;
    const totalAmount = taxableAmount + taxAmount;

    subtotal += totalAmount;

    return {
      ...item,
      discount_amount: itemDiscountAmount,
      taxable_amount: taxableAmount,
      cgst_amount: cgstAmount,
      sgst_amount: sgstAmount,
      igst_amount: igstAmount,
      cess_amount: cessAmount,
      tax_amount: taxAmount,
      total_amount: totalAmount,
      sort_order: index + 1
    };
  });

  // Calculate sale level discount
  const discountAmount = discount_type === 'percentage'
    ? (subtotal * discount_value) / 100
    : (discount_value || 0);

  const taxAmount = processedItems.reduce((sum, item) => sum + item.tax_amount, 0);
  const totalAmount = subtotal - discountAmount + parseFloat(shipping_amount || 0) + parseFloat(adjustment_amount || 0);
  const balanceAmount = totalAmount; // Initially, balance equals total

  // Create sale
  const sale = await Sales.create({
    company_id: req.user.company_id,
    customer_id,
    estimation_id,
    proforma_id,
    sales_date,
    delivery_date,
    subtotal,
    discount_type,
    discount_value,
    discount_amount,
    tax_amount: taxAmount,
    shipping_amount,
    adjustment_amount,
    total_amount,
    paid_amount: 0,
    balance_amount: balanceAmount,
    terms_conditions,
    notes,
    internal_notes,
    billing_address,
    shipping_address,
    payment_terms,
    delivery_terms,
    reference_number,
    po_number,
    created_by: req.user.id
  });

  // Create sale items
  if (processedItems.length > 0) {
    const itemsToCreate = processedItems.map(item => ({
      ...item,
      sales_id: sale.id
    }));

    await SalesItem.bulkCreate(itemsToCreate);
  }

  // Fetch created sale with associations
  const createdSale = await Sales.findByPk(sale.id, {
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: SalesItem,
        as: 'items',
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'product_name', 'product_code', 'unit']
          }
        ]
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      sale: createdSale
    }
  });
});

// Update sale
const updateSale = catchAsync(async (req, res, next) => {
  const sale = await Sales.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!sale) {
    return next(new AppError('Sale not found', 404));
  }

  // Check if sale can be updated
  if (sale.status === 'delivered' || sale.status === 'cancelled') {
    return next(new AppError('Cannot update delivered or cancelled sale', 400));
  }

  const allowedUpdates = [
    'delivery_date', 'status', 'payment_status', 'terms_conditions', 'notes',
    'internal_notes', 'billing_address', 'shipping_address', 'payment_terms',
    'delivery_terms', 'reference_number', 'po_number', 'adjustment_amount'
  ];

  const updates = {};
  allowedUpdates.forEach(field => {
    if (req.body[field] !== undefined) {
      updates[field] = req.body[field];
    }
  });

  // Recalculate total if adjustment amount changed
  if (updates.adjustment_amount !== undefined) {
    updates.total_amount = sale.subtotal - sale.discount_amount +
                          sale.shipping_amount + parseFloat(updates.adjustment_amount || 0);
    updates.balance_amount = updates.total_amount - sale.paid_amount;
  }

  updates.updated_by = req.user.id;

  await sale.update(updates);

  const updatedSale = await Sales.findByPk(sale.id, {
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: SalesItem,
        as: 'items'
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      sale: updatedSale
    }
  });
});

// Delete sale
const deleteSale = catchAsync(async (req, res, next) => {
  const sale = await Sales.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!sale) {
    return next(new AppError('Sale not found', 404));
  }

  if (sale.status === 'delivered' || sale.paid_amount > 0) {
    return next(new AppError('Cannot delete delivered sale or sale with payments', 400));
  }

  await sale.destroy();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

// Get sales statistics
const getSalesStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  const totalSales = await Sales.count({
    where: { company_id: companyId }
  });

  const statusStats = await Sales.findAll({
    where: { company_id: companyId },
    attributes: [
      'status',
      [Sales.sequelize.fn('COUNT', Sales.sequelize.col('id')), 'count'],
      [Sales.sequelize.fn('SUM', Sales.sequelize.col('total_amount')), 'total_amount']
    ],
    group: ['status'],
    raw: true
  });

  const paymentStats = await Sales.findAll({
    where: { company_id: companyId },
    attributes: [
      'payment_status',
      [Sales.sequelize.fn('COUNT', Sales.sequelize.col('id')), 'count'],
      [Sales.sequelize.fn('SUM', Sales.sequelize.col('total_amount')), 'total_amount']
    ],
    group: ['payment_status'],
    raw: true
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_sales: totalSales,
      status_stats: statusStats,
      payment_stats: paymentStats
    }
  });
});

module.exports = {
  getSales,
  getSale,
  createSale,
  updateSale,
  deleteSale,
  getSalesStats
};
