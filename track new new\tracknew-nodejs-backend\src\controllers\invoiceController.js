const { Invoice, InvoiceItem, Customer, Company, User, Product, Sales, Service, Proforma } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all invoices with filtering and pagination
const getInvoices = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    status,
    payment_status,
    invoice_type,
    customer_id,
    search,
    sort_by = 'created_at',
    sort_order = 'DESC',
    date_from,
    date_to
  } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = { company_id: req.user.company_id };

  // Apply filters
  if (status) {
    whereClause.status = status;
  }

  if (payment_status) {
    whereClause.payment_status = payment_status;
  }

  if (invoice_type) {
    whereClause.invoice_type = invoice_type;
  }

  if (customer_id) {
    whereClause.customer_id = customer_id;
  }

  if (date_from && date_to) {
    whereClause.invoice_date = {
      [Op.between]: [new Date(date_from), new Date(date_to)]
    };
  }

  if (search) {
    whereClause[Op.or] = [
      { invoice_number: { [Op.like]: `%${search}%` } },
      { reference_number: { [Op.like]: `%${search}%` } },
      { po_number: { [Op.like]: `%${search}%` } },
      { notes: { [Op.like]: `%${search}%` } }
    ];
  }

  const { count, rows: invoices } = await Invoice.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'customer_code', 'email', 'mobile_number']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: InvoiceItem,
        as: 'items',
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'product_name', 'product_code', 'unit']
          }
        ]
      }
    ],
    order: [[sort_by, sort_order.toUpperCase()]],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.status(200).json({
    status: 'success',
    data: {
      invoices,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: count,
        total_pages: Math.ceil(count / limit)
      }
    }
  });
});

// Get invoice by ID
const getInvoice = catchAsync(async (req, res, next) => {
  const invoice = await Invoice.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    },
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: Company,
        as: 'company'
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: Sales,
        as: 'sales',
        attributes: ['id', 'sales_number', 'status']
      },
      {
        model: Service,
        as: 'service',
        attributes: ['id', 'service_code', 'status']
      },
      {
        model: Proforma,
        as: 'proforma',
        attributes: ['id', 'proforma_number', 'status']
      },
      {
        model: InvoiceItem,
        as: 'items',
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'product_name', 'product_code', 'unit', 'hsn_code']
          }
        ],
        order: [['sort_order', 'ASC']]
      }
    ]
  });

  if (!invoice) {
    return next(new AppError('Invoice not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      invoice
    }
  });
});

// Create new invoice
const createInvoice = catchAsync(async (req, res, next) => {
  const {
    customer_id,
    sales_id,
    service_id,
    proforma_id,
    invoice_date = new Date(),
    due_date,
    invoice_type = 'sales',
    items = [],
    discount_type,
    discount_value = 0,
    shipping_amount = 0,
    adjustment_amount = 0,
    terms_conditions,
    notes,
    internal_notes,
    billing_address,
    shipping_address,
    payment_terms,
    reference_number,
    po_number
  } = req.body;

  // Validate customer exists
  const customer = await Customer.findOne({
    where: { id: customer_id, company_id: req.user.company_id }
  });

  if (!customer) {
    return next(new AppError('Customer not found', 404));
  }

  // Calculate totals
  let subtotal = 0;
  const processedItems = items.map((item, index) => {
    const lineTotal = item.quantity * item.unit_price;
    const itemDiscountAmount = item.discount_type === 'percentage' 
      ? (lineTotal * item.discount_value) / 100 
      : (item.discount_value || 0);
    const taxableAmount = lineTotal - itemDiscountAmount;
    
    const cgstAmount = (taxableAmount * (item.cgst_rate || 0)) / 100;
    const sgstAmount = (taxableAmount * (item.sgst_rate || 0)) / 100;
    const igstAmount = (taxableAmount * (item.igst_rate || 0)) / 100;
    const cessAmount = (taxableAmount * (item.cess_rate || 0)) / 100;
    
    const taxAmount = cgstAmount + sgstAmount + igstAmount + cessAmount;
    const totalAmount = taxableAmount + taxAmount;
    
    subtotal += totalAmount;
    
    return {
      ...item,
      discount_amount: itemDiscountAmount,
      taxable_amount: taxableAmount,
      cgst_amount: cgstAmount,
      sgst_amount: sgstAmount,
      igst_amount: igstAmount,
      cess_amount: cessAmount,
      tax_amount: taxAmount,
      total_amount: totalAmount,
      sort_order: index + 1
    };
  });

  // Calculate invoice level discount
  const discountAmount = discount_type === 'percentage' 
    ? (subtotal * discount_value) / 100 
    : (discount_value || 0);

  const taxAmount = processedItems.reduce((sum, item) => sum + item.tax_amount, 0);
  const totalAmount = subtotal - discountAmount + parseFloat(shipping_amount || 0) + parseFloat(adjustment_amount || 0);
  const balanceAmount = totalAmount; // Initially, balance equals total

  // Create invoice
  const invoice = await Invoice.create({
    company_id: req.user.company_id,
    customer_id,
    sales_id,
    service_id,
    proforma_id,
    invoice_date,
    due_date,
    invoice_type,
    subtotal,
    discount_type,
    discount_value,
    discount_amount,
    tax_amount: taxAmount,
    shipping_amount,
    adjustment_amount,
    total_amount,
    paid_amount: 0,
    balance_amount: balanceAmount,
    terms_conditions,
    notes,
    internal_notes,
    billing_address,
    shipping_address,
    payment_terms,
    reference_number,
    po_number,
    created_by: req.user.id
  });

  // Create invoice items
  if (processedItems.length > 0) {
    const itemsToCreate = processedItems.map(item => ({
      ...item,
      invoice_id: invoice.id
    }));
    
    await InvoiceItem.bulkCreate(itemsToCreate);
  }

  // Fetch created invoice with associations
  const createdInvoice = await Invoice.findByPk(invoice.id, {
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: InvoiceItem,
        as: 'items',
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'product_name', 'product_code', 'unit']
          }
        ]
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      invoice: createdInvoice
    }
  });
});

// Update invoice
const updateInvoice = catchAsync(async (req, res, next) => {
  const invoice = await Invoice.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!invoice) {
    return next(new AppError('Invoice not found', 404));
  }

  // Check if invoice can be updated
  if (invoice.status === 'paid') {
    return next(new AppError('Cannot update paid invoice', 400));
  }

  const allowedUpdates = [
    'due_date', 'status', 'payment_status', 'terms_conditions', 'notes', 
    'internal_notes', 'billing_address', 'shipping_address', 'payment_terms',
    'reference_number', 'po_number', 'adjustment_amount'
  ];

  const updates = {};
  allowedUpdates.forEach(field => {
    if (req.body[field] !== undefined) {
      updates[field] = req.body[field];
    }
  });

  // Recalculate total if adjustment amount changed
  if (updates.adjustment_amount !== undefined) {
    updates.total_amount = invoice.subtotal - invoice.discount_amount + 
                          invoice.shipping_amount + parseFloat(updates.adjustment_amount || 0);
    updates.balance_amount = updates.total_amount - invoice.paid_amount;
  }

  updates.updated_by = req.user.id;

  await invoice.update(updates);

  const updatedInvoice = await Invoice.findByPk(invoice.id, {
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: InvoiceItem,
        as: 'items'
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      invoice: updatedInvoice
    }
  });
});

// Delete invoice
const deleteInvoice = catchAsync(async (req, res, next) => {
  const invoice = await Invoice.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!invoice) {
    return next(new AppError('Invoice not found', 404));
  }

  if (invoice.status === 'paid' || invoice.paid_amount > 0) {
    return next(new AppError('Cannot delete invoice with payments', 400));
  }

  await invoice.destroy();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

module.exports = {
  getInvoices,
  getInvoice,
  createInvoice,
  updateInvoice,
  deleteInvoice
};
